"""Command for processing a batch of agent events."""

from dataclasses import dataclass


@dataclass(frozen=True)
class ProcessAgentEventsBatchCommand:
    """Command to process a batch of agent events from XML.

    This command encapsulates all the data needed to process multiple agent events
    in a single batch operation for better performance.

    Attributes:
        xml_contents: List of raw XML content strings
        sqs_message_ids: List of SQS message IDs corresponding to each XML content
        client_timezone: Client timezone for timestamp conversion (default: America/Chicago)
    """

    xml_contents: list[str]
    sqs_message_ids: list[str]
    client_timezone: str = "America/New_York"

    def __post_init__(self) -> None:
        """Validate that xml_contents and sqs_message_ids have the same length."""
        if len(self.xml_contents) != len(self.sqs_message_ids):
            msg = (
                f"xml_contents and sqs_message_ids must have the same length. "
                f"Got {len(self.xml_contents)} and {len(self.sqs_message_ids)}"
            )
            raise ValueError(msg)
