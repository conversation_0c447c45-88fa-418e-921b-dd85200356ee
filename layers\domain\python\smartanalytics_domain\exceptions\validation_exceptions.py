"""Validation-related domain exceptions."""

from typing import Any

from smartanalytics_domain.exceptions.base import DomainError


class ValidationError(DomainError):
    """Exception raised when domain validation fails."""

    def __init__(
        self,
        message: str,
        field_name: str | None = None,
        field_value: Any | None = None,
        validation_rule: str | None = None,
        context: dict[str, Any] | None = None,
    ):
        """Initialize validation error.

        Args:
            message: Error message
            field_name: Name of field that failed validation
            field_value: Value that failed validation
            validation_rule: Name of validation rule that failed
            context: Additional context information
        """
        context = context or {}
        if field_name:
            context["field_name"] = field_name
        if field_value is not None:
            context["field_value"] = field_value
        if validation_rule:
            context["validation_rule"] = validation_rule

        super().__init__(
            message,
            error_code="VALIDATION_ERROR",
            context=context,
        )
        self.field_name = field_name
        self.field_value = field_value
        self.validation_rule = validation_rule


class BusinessRuleViolationError(DomainError):
    """Exception raised when a business rule is violated."""

    def __init__(
        self,
        message: str,
        rule_name: str | None = None,
        rule_description: str | None = None,
        context: dict[str, Any] | None = None,
    ):
        """Initialize business rule violation error.

        Args:
            message: Error message
            rule_name: Name of business rule that was violated
            rule_description: Description of the business rule
            context: Additional context information
        """
        context = context or {}
        if rule_name:
            context["rule_name"] = rule_name
        if rule_description:
            context["rule_description"] = rule_description

        super().__init__(
            message,
            error_code="BUSINESS_RULE_VIOLATION",
            context=context,
        )
        self.rule_name = rule_name
        self.rule_description = rule_description


class MultipleValidationError(ValidationError):
    """Exception raised when multiple validation errors occur."""

    def __init__(
        self,
        message: str,
        validation_errors: list[ValidationError],
        context: dict[str, Any] | None = None,
    ):
        """Initialize multiple validation error.

        Args:
            message: Error message
            validation_errors: List of individual validation errors
            context: Additional context information
        """
        context = context or {}
        context["error_count"] = len(validation_errors)
        context["errors"] = [error.to_dict() for error in validation_errors]

        super().__init__(
            message,
            validation_rule="multiple_validation_errors",
            context=context,
        )
        self.validation_errors = validation_errors

    def get_field_errors(self) -> dict[str, list[str]]:
        """Get validation errors grouped by field name.

        Returns:
            Dictionary mapping field names to lists of error messages
        """
        field_errors: dict[str, list[str]] = {}

        for error in self.validation_errors:
            field_name = error.field_name or "general"
            if field_name not in field_errors:
                field_errors[field_name] = []
            field_errors[field_name].append(error.message)

        return field_errors


class RequiredFieldError(ValidationError):
    """Exception raised when a required field is missing or empty."""

    def __init__(
        self,
        field_name: str,
        entity_type: str | None = None,
    ):
        """Initialize required field error.

        Args:
            field_name: Name of required field
            entity_type: Type of entity being validated
        """
        message = f"Required field '{field_name}' is missing or empty"
        if entity_type:
            message = (
                f"Required field '{field_name}' is missing or empty for {entity_type}"
            )

        context = {}
        if entity_type:
            context["entity_type"] = entity_type

        super().__init__(
            message,
            field_name=field_name,
            validation_rule="required_field",
            context=context,
        )
        self.entity_type = entity_type


class InvalidFormatError(ValidationError):
    """Exception raised when a field value has an invalid format."""

    def __init__(
        self,
        field_name: str,
        field_value: Any,
        expected_format: str,
        entity_type: str | None = None,
    ):
        """Initialize invalid format error.

        Args:
            field_name: Name of field with invalid format
            field_value: Invalid field value
            expected_format: Description of expected format
            entity_type: Type of entity being validated
        """
        message = (
            f"Field '{field_name}' has invalid format. Expected: {expected_format}"
        )
        if entity_type:
            message = f"Field '{field_name}' has invalid format for {entity_type}. Expected: {expected_format}"

        context = {}
        if entity_type:
            context["entity_type"] = entity_type
        context["expected_format"] = expected_format

        super().__init__(
            message,
            field_name=field_name,
            field_value=field_value,
            validation_rule="invalid_format",
            context=context,
        )
        self.expected_format = expected_format
        self.entity_type = entity_type


class ValueOutOfRangeError(ValidationError):
    """Exception raised when a field value is outside the allowed range."""

    def __init__(
        self,
        field_name: str,
        field_value: Any,
        min_value: Any | None = None,
        max_value: Any | None = None,
        entity_type: str | None = None,
    ):
        """Initialize value out of range error.

        Args:
            field_name: Name of field with out-of-range value
            field_value: Out-of-range field value
            min_value: Minimum allowed value
            max_value: Maximum allowed value
            entity_type: Type of entity being validated
        """
        range_desc = ""
        if min_value is not None and max_value is not None:
            range_desc = f"between {min_value} and {max_value}"
        elif min_value is not None:
            range_desc = f"at least {min_value}"
        elif max_value is not None:
            range_desc = f"at most {max_value}"

        message = f"Field '{field_name}' value {field_value} is out of range. Expected: {range_desc}"
        if entity_type:
            message = f"Field '{field_name}' value {field_value} is out of range for {entity_type}. Expected: {range_desc}"

        context = {}
        if entity_type:
            context["entity_type"] = entity_type
        if min_value is not None:
            context["min_value"] = min_value
        if max_value is not None:
            context["max_value"] = max_value

        super().__init__(
            message,
            field_name=field_name,
            field_value=field_value,
            validation_rule="value_out_of_range",
            context=context,
        )
        self.min_value = min_value
        self.max_value = max_value
        self.entity_type = entity_type
