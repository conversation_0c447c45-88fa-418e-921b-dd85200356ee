"""Command for getting or creating an agent."""

from dataclasses import dataclass


@dataclass(frozen=True)
class GetOrCreateAgentCommand:
    """Command to get existing agent or create new one.

    Attributes:
        tenant_key: Tenant key (foreign key)
        agent_name: Agent identifier
        agent_role: Agent role (optional)
    """

    tenant_key: int
    agent_name: str
    agent_role: str | None = None
