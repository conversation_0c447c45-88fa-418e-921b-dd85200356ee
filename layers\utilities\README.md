# Utilities Layer

The Utilities Layer provides shared functionality and common utilities used across the SmartAnalytics Agent Event Processor. This layer contains configuration management, logging, datetime handling, and other cross-cutting concerns.

## Architecture

```mermaid
graph TB
    subgraph "Utilities Layer"
        direction LR
        A[Configuration Management] --> B[Logging Helpers]
        A --> C[DateTime Helpers]
        A --> D[Hash/Event Helpers]
        A --> E[XML Parsing Utilities]
        A --> F[AWS Powertools]
    end
```

## Components

### Configuration (`config/`)
Centralized configuration management:
- `database_config.py` - Database connection configuration
- Environment variable handling with validation
- Type-safe configuration objects

### DateTime Helper (`datetime_helper.py`)
Timezone and datetime utilities:
- Timezone conversion and handling
- UTC/local time conversions
- Date formatting and parsing
- Support for client-specific timezones

### Hash Helper (`hash_helper.py`)
Event hashing and deduplication:
- SHA-256 event hashing for deduplication
- Consistent hash generation across events
- Event fingerprinting for duplicate detection

### Logging Helper (`logging_helper.py`)
Structured logging configuration:
- AWS Lambda Powertools integration
- JSON structured logging
- Correlation ID tracking
- Performance metrics logging

### XML Helper (`xml_helper.py`)
XML parsing and processing utilities:
- Safe XML parsing with validation
- XML to JSON conversion
- Event extraction from XML payloads
- Error handling for malformed XML

### Event Helper (`event_helper.py`)
Event processing utilities:
- Event validation and normalization
- Event type detection and classification
- Event data extraction and transformation

## Key Features

### Timezone Management
```python
from smartanalytics_utilities.datetime_helper import convert_to_timezone

# Convert UTC to client timezone
local_time = convert_to_timezone(utc_time, "America/Chicago")
```

### Event Deduplication
```python
from smartanalytics_utilities.hash_helper import generate_event_hash

# Generate consistent hash for deduplication
event_hash = generate_event_hash(xml_content)
```

### Structured Logging
```python
from smartanalytics_utilities.logging_helper import get_logger

logger = get_logger(__name__)
logger.info("Processing event", extra={"event_type": "Login", "agent": "agent001"})
```

### XML Processing
```python
from smartanalytics_utilities.xml_helper import xml_to_json

# Convert XML to structured JSON
event_data = xml_to_json(xml_content)
```

## Dependencies

```txt
pydantic>=2.0.0                    # Data validation and settings
aws-lambda-powertools>=2.0.0       # AWS Lambda utilities
python-dateutil>=2.8.0             # Date parsing and manipulation
pytz>=2023.3                       # Timezone handling
lxml>=4.9.0                        # XML parsing
```

## Configuration

### Environment Variables
```bash
# Logging Configuration
LOG_LEVEL=INFO
POWERTOOLS_SERVICE_NAME=smartanalytics-processor
POWERTOOLS_METRICS_NAMESPACE=SmartAnalytics

# Timezone Configuration
DEFAULT_TIMEZONE=UTC
CLIENT_TIMEZONE=America/Chicago

# Event Processing
ENABLE_EVENT_DEDUPLICATION=true
HASH_ALGORITHM=sha256
```

## Usage Examples

### Database Configuration
```python
from smartanalytics_utilities.config.database_config import DatabaseConfig

# Load configuration from environment
config = DatabaseConfig()
print(f"Connecting to: {config.host}:{config.port}/{config.database}")
```

### Event Processing Pipeline
```python
from smartanalytics_utilities.xml_helper import xml_to_json
from smartanalytics_utilities.hash_helper import generate_event_hash
from smartanalytics_utilities.datetime_helper import convert_to_timezone

# Process incoming XML event
event_data = xml_to_json(xml_content)
event_hash = generate_event_hash(xml_content)
local_time = convert_to_timezone(event_data['timestamp'], client_timezone)
```

## Performance Considerations

### Lambda Optimization
- **Minimal Dependencies**: Only essential utilities included
- **Fast Imports**: Optimized module loading
- **Memory Efficient**: Lightweight helper functions

### Caching
- Configuration values cached after first load
- Timezone objects cached for reuse
- XML parser instances reused when possible

## Testing

Utilities layer testing includes:
- Unit tests for all helper functions
- Configuration validation tests
- Timezone conversion accuracy tests
- XML parsing edge case tests
- Hash consistency verification tests

## Integration

The utilities layer is used by:
- **Lambda Functions**: For logging and configuration
- **Domain Layer**: For datetime and validation utilities
- **Infrastructure Layer**: For database configuration
- **All Layers**: For common functionality and cross-cutting concerns
