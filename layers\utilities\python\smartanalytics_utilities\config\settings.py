"""Main application settings with hierarchical configuration."""

from functools import lru_cache
from typing import Any

from pydantic import Field
from pydantic_settings import SettingsConfigDict
from smartanalytics_utilities.config.aws_config import AWSConfig
from smartanalytics_utilities.config.base import BaseConfig
from smartanalytics_utilities.config.database_config import DatabaseConfig
from smartanalytics_utilities.config.logging_config import LoggingConfig


class Settings(BaseConfig):
    """Main application settings with nested configuration."""

    # Application metadata
    app_name: str = Field(
        default="smartanalytics-agent-event-processor", description="Application name"
    )

    app_description: str = Field(
        default="SmartAnalytics Agent Event Processor",
        description="Application description",
    )

    # Nested configuration
    aws: AWSConfig = Field(default_factory=AWSConfig)

    database: DatabaseConfig = Field(default_factory=DatabaseConfig)

    logging: LoggingConfig = Field(default_factory=LoggingConfig)

    # Processing configuration
    batch_size: int = Field(
        default=10, ge=1, le=100, description="Maximum batch size for processing"
    )

    max_retries: int = Field(
        default=3,
        ge=0,
        le=10,
        description="Maximum number of retries for failed operations",
    )

    retry_delay_seconds: float = Field(
        default=1.0, ge=0.1, le=60.0, description="Delay between retries in seconds"
    )

    # Feature flags
    enable_metrics: bool = Field(default=True, description="Enable metrics collection")

    enable_tracing: bool = Field(default=True, description="Enable distributed tracing")

    enable_performance_logging: bool = Field(
        default=True, description="Enable performance logging"
    )

    # Client configuration
    client_timezone: str = Field(
        default="America/Chicago", description="Default client timezone"
    )

    hash_algorithm: str = Field(
        default="sha256", description="Hash algorithm for event deduplication"
    )

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        validate_assignment=True,
        extra="forbid",
        str_strip_whitespace=True,
        env_nested_delimiter="_",
    )

    def get_database_url(self) -> str:
        """Get database connection URL."""
        return self.database.get_connection_url()

    def get_powertools_config(self) -> dict[str, Any]:
        """Get AWS Lambda Powertools configuration."""
        config = self.logging.get_powertools_config()
        config.update(
            {
                "service": self.app_name,
                "namespace": self.aws.cloudwatch_namespace,
            }
        )
        return config

    def is_feature_enabled(self, feature: str) -> bool:
        """Check if a feature is enabled."""
        feature_map = {
            "metrics": self.enable_metrics,
            "tracing": self.enable_tracing,
            "performance_logging": self.enable_performance_logging,
        }
        return feature_map.get(feature, False)


# Global settings instance with caching
@lru_cache
def get_settings() -> Settings:
    """Get cached application settings instance."""
    return Settings()
