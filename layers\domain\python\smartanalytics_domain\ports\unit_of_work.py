"""Unit of Work interface for managing transactions."""

import types
from abc import ABC, abstractmethod

from smartanalytics_domain.ports.repositories import (
    AgentEventRepository,
    AgentRepository,
    RingGroupRepository,
    TenantRepository,
)


class UnitOfWork(ABC):
    """Unit of Work interface for managing database transactions."""

    # Repository properties
    agent_events: AgentEventRepository
    agents: AgentRepository
    tenants: TenantRepository
    ring_groups: RingGroupRepository

    @abstractmethod
    async def __aenter__(self) -> "UnitOfWork":
        """Enter the async context manager."""
        raise NotImplementedError

    @abstractmethod
    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: types.TracebackType | None,
    ) -> None:
        """Exit the async context manager."""
        raise NotImplementedError

    @abstractmethod
    async def commit(self) -> None:
        """Commit the current transaction."""
        raise NotImplementedError

    @abstractmethod
    async def rollback(self) -> None:
        """Rollback the current transaction."""
        raise NotImplementedError
