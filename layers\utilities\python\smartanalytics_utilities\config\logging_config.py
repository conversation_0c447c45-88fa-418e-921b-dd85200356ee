"""Logging configuration for SmartAnalytics applications."""

from typing import Any

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class LoggingConfig(BaseSettings):
    """Logging configuration."""

    # Log level settings
    log_level: str = Field(default="INFO", description="Default log level")

    root_log_level: str = Field(default="WARNING", description="Root logger level")

    # AWS Lambda Powertools settings
    powertools_service_name: str = Field(
        default="smartanalytics", description="Service name for AWS Lambda Powertools"
    )

    powertools_log_level: str = Field(
        default="INFO", description="Log level for AWS Lambda Powertools"
    )

    powertools_logger_sample_rate: float = Field(
        default=0.1, ge=0.0, le=1.0, description="Sample rate for debug logs"
    )

    powertools_logger_stream: str | None = Field(
        default=None, description="Log stream override"
    )

    # Structured logging settings
    json_logging: bool = Field(
        default=True, description="Enable JSON structured logging"
    )

    include_timestamp: bool = Field(
        default=True, description="Include timestamp in log messages"
    )

    include_caller_info: bool = Field(
        default=False, description="Include caller information in logs"
    )

    # Correlation settings
    correlation_id_header: str = Field(
        default="x-correlation-id", description="Header name for correlation ID"
    )

    auto_correlation_id: bool = Field(
        default=True, description="Automatically generate correlation IDs"
    )

    # Performance logging
    log_performance_metrics: bool = Field(
        default=True, description="Log performance metrics"
    )

    slow_query_threshold_ms: int = Field(
        default=1000,
        ge=0,
        description="Threshold for logging slow queries (milliseconds)",
    )

    # External logging
    cloudwatch_log_group: str | None = Field(
        default=None, description="CloudWatch log group name"
    )

    cloudwatch_log_stream: str | None = Field(
        default=None, description="CloudWatch log stream name"
    )

    model_config = SettingsConfigDict(
        env_prefix="LOGGING_",
        case_sensitive=False,
        str_strip_whitespace=True,
        validate_assignment=True,
        extra="ignore",  # Changed from "forbid" to "ignore" to allow extra env vars
    )

    def get_powertools_config(self) -> dict[str, Any]:
        """Get AWS Lambda Powertools logging configuration."""
        return {
            "service": self.powertools_service_name,
            "level": self.powertools_log_level,
            "sample_rate": self.powertools_logger_sample_rate,
            "stream": self.powertools_logger_stream,
            "json_serializer": self.json_logging,
            "datefmt": "%Y-%m-%d %H:%M:%S" if self.include_timestamp else None,
        }

    def get_log_format(self) -> str:
        """Get log format string."""
        if self.json_logging:
            return "%(message)s"

        format_parts = []

        if self.include_timestamp:
            format_parts.append("%(asctime)s")

        format_parts.extend(
            [
                "%(levelname)s",
                "%(name)s",
            ]
        )

        if self.include_caller_info:
            format_parts.append("%(filename)s:%(lineno)d")

        format_parts.append("%(message)s")

        return " - ".join(format_parts)
