"""Configuration management for SmartAnalytics applications.

This module provides configuration classes and utilities for managing application
settings across different environments (dev, qa, prod) with support for:

- Environment variable loading
- AWS Parameter Store integration
- AWS Secrets Manager integration
- Type-safe configuration with Pydantic
- Hierarchical configuration structure
"""

from smartanalytics_utilities.config.aws_config import AWSConfig
from smartanalytics_utilities.config.base import BaseConfig, Environment
from smartanalytics_utilities.config.database_config import DatabaseConfig
from smartanalytics_utilities.config.logging_config import LoggingConfig
from smartanalytics_utilities.config.settings import Settings, get_settings

__all__ = [
    "AWSConfig",
    "BaseConfig",
    "DatabaseConfig",
    "Environment",
    "LoggingConfig",
    "Settings",
    "get_settings",
]
