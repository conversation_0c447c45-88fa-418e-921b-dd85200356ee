"""Hash generation utilities for event deduplication."""

import hashlib
import j<PERSON>
from typing import Any, ClassV<PERSON>


def generate_event_hash(
    event_data: dict[str, Any],
    algorithm: str | None = None,
    exclude_fields: list[str] | None = None,
) -> str:
    """Generate deterministic hash for event data.

    Args:
        event_data: Event data dictionary
        algorithm: Hash algorithm to use (default from settings)
        exclude_fields: Fields to exclude from hash calculation

    Returns:
        Hexadecimal hash string

    Raises:
        ValueError: If hash algorithm is not supported
    """
    if algorithm is None:
        algorithm = "sha256"  # Default to SHA-256

    # Validate algorithm
    if algorithm not in hashlib.algorithms_available:
        raise ValueError(f"Unsupported hash algorithm: {algorithm}")

    # Prepare data for hashing
    hash_data = _prepare_data_for_hash(event_data, exclude_fields or [])

    # Create hash
    hasher = hashlib.new(algorithm)
    hasher.update(hash_data.encode("utf-8"))

    return hasher.hexdigest()


def hash_event(event_data: dict[str, Any]) -> str:
    """Generate hash for event data.

    This function provides backward compatibility with the existing codebase.

    Args:
        event_data: Event data dictionary

    Returns:
        Hexadecimal hash string
    """
    return generate_event_hash(event_data)


def _prepare_data_for_hash(
    data: dict[str, Any],
    exclude_fields: list[str],
) -> str:
    """Prepare data for hash calculation.

    Args:
        data: Data dictionary
        exclude_fields: Fields to exclude from hash

    Returns:
        JSON string representation of data for hashing
    """
    # Create a copy of data excluding specified fields
    hash_data = {key: value for key, value in data.items() if key not in exclude_fields}

    # Sort keys for deterministic hash
    return json.dumps(hash_data, sort_keys=True, separators=(",", ":"))


class HashGenerator:
    """Hash generator with configurable options."""

    def __init__(
        self,
        algorithm: str = "sha256",
        exclude_fields: list[str] | None = None,
    ):
        """Initialize hash generator.

        Args:
            algorithm: Hash algorithm to use
            exclude_fields: Default fields to exclude from hash calculation
        """
        if algorithm not in hashlib.algorithms_available:
            raise ValueError(f"Unsupported hash algorithm: {algorithm}")

        self.algorithm = algorithm
        self.exclude_fields = exclude_fields or []

    def generate(
        self,
        data: dict[str, Any],
        additional_exclude_fields: list[str] | None = None,
    ) -> str:
        """Generate hash for data.

        Args:
            data: Data to hash
            additional_exclude_fields: Additional fields to exclude

        Returns:
            Hexadecimal hash string
        """
        exclude_fields = self.exclude_fields.copy()
        if additional_exclude_fields:
            exclude_fields.extend(additional_exclude_fields)

        return generate_event_hash(
            data,
            algorithm=self.algorithm,
            exclude_fields=exclude_fields,
        )

    def verify(self, data: dict[str, Any], expected_hash: str) -> bool:
        """Verify data against expected hash.

        Args:
            data: Data to verify
            expected_hash: Expected hash value

        Returns:
            True if hash matches, False otherwise
        """
        actual_hash = self.generate(data)
        return actual_hash == expected_hash


class EventHasher:
    """Specialized hasher for agent events."""

    # Fields that should not be included in event hash
    EXCLUDE_FIELDS: ClassVar[list[str]] = [
        "dateCreated",
        "id",
        "event_key",
        "created_at",
        "updated_at",
    ]

    def __init__(self) -> None:
        """Initialize event hasher."""
        self.generator = HashGenerator(
            algorithm="sha256",
            exclude_fields=self.EXCLUDE_FIELDS,
        )

    def hash_agent_event(self, event_data: dict[str, Any]) -> str:
        """Generate hash for agent event data.

        Args:
            event_data: Agent event data

        Returns:
            Event hash string
        """
        return self.generator.generate(event_data)

    def is_duplicate(
        self,
        event_data: dict[str, Any],
        existing_hash: str,
    ) -> bool:
        """Check if event is a duplicate.

        Args:
            event_data: Event data to check
            existing_hash: Hash of existing event

        Returns:
            True if event is a duplicate, False otherwise
        """
        return self.generator.verify(event_data, existing_hash)
