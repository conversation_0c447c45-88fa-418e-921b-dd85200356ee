"""Domain exceptions for SmartAnalytics applications.

This module contains domain-specific exceptions that represent business rule
violations and domain-level errors. These exceptions should be caught and
handled appropriately by the application layer.
"""

from smartanalytics_domain.exceptions.base import DomainError
from smartanalytics_domain.exceptions.event_exceptions import (
    EventProcessingError,
    InvalidEventDataError,
)
from smartanalytics_domain.exceptions.validation_exceptions import (
    BusinessRuleViolationError,
    ValidationError,
)

__all__ = [
    "BusinessRuleViolationError",
    "DomainError",
    "EventProcessingError",
    "InvalidEventDataError",
    "ValidationError",
]
