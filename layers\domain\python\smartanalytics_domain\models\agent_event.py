"""Agent event domain model for dimensional data warehouse."""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any

from smartanalytics_utilities.utils.hash_helper import generate_event_hash

# Constants
SHA256_HASH_LENGTH = 64
MAX_BATCH_SIZE = 100


class EventType(str, Enum):
    """Agent event types for ACD processing."""

    LOGIN = "Login"
    LOGOUT = "Logout"
    AGENT_AVAILABLE = "AgentAvailable"
    AGENT_BUSIED_OUT = "AgentBusiedOut"
    ACD_LOGIN = "ACDLogin"
    ACD_LOGOUT = "ACDLogout"


@dataclass
class AgentEvent:
    """Agent event domain model matching dim_agent_event table.

    This model represents the business logic for agent events and maps
    directly to the stored procedure parameters and table structure.
    """

    # Core identifiers (business keys)
    tenant_id: str
    agent_id: str
    event_type: EventType
    event_hash: str

    # Required Timestamps
    timestamp_utc: datetime
    timestamp_local: datetime

    # Required string fields
    tenant_name: str
    timezone_name: str
    agent_name: str

    # Optional agent details
    agent_role: str | None = None

    # Event details (optional\
    operator_id: str | None = None
    workstation: str | None = None
    media_label: str | None = None
    uri: str | None = None
    device_name: str | None = None
    busied_out_action: str | None = None
    busied_out_duration: int | None = None
    reason_code: str | None = None

    # Ring group details
    ring_group_name: str | None = None
    ring_group_uri: str | None = None

    # JSON data (required, can be empty dict)
    json_data: dict[str, Any] = field(default_factory=dict)

    def __post_init__(self) -> None:
        """Validate the agent event."""
        # Validate required fields
        if not self.tenant_name:
            raise ValueError("tenant_name is required")
        if not self.agent_name:
            raise ValueError("agent_name is required")
        if not self.event_type:
            raise ValueError("event_type is required")
        if len(self.event_hash) != SHA256_HASH_LENGTH:
            raise ValueError("hash_event must be 64-character SHA-256")
        if not self.timestamp_utc or not self.timestamp_local:
            raise ValueError("Both timestamp_utc and timestamp_local are required")

    @property
    def is_login_event(self) -> bool:
        """Check if this is a login event."""
        return self.event_type in [EventType.LOGIN, EventType.ACD_LOGIN]

    @property
    def is_logout_event(self) -> bool:
        """Check if this is a logout event."""
        return self.event_type in [EventType.LOGOUT, EventType.ACD_LOGOUT]

    @property
    def is_state_change_event(self) -> bool:
        """Check if this is a state change event."""
        return self.event_type in [
            EventType.AGENT_AVAILABLE,
            EventType.AGENT_BUSIED_OUT,
        ]

    @property
    def has_ring_group(self) -> bool:
        """Check if this event has ring group information."""
        return bool(self.ring_group_name and self.ring_group_uri)

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "AgentEvent":
        """Create AgentEvent from dictionary.

        Args:
            data: Dictionary containing event data

        Returns:
            AgentEvent instance
        """

        # Convert event_type string to EventType enum
        event_type = data.get("event_type")
        if isinstance(event_type, str):
            event_type = EventType(event_type)
        if not isinstance(event_type, EventType):
            raise ValueError(f"Invalid event_type: {event_type}")

        # Generate event hash if not provided
        event_hash = data.get("event_hash")
        if not event_hash:
            event_hash = generate_event_hash(data.get("json_data", {}))

        return cls(
            tenant_id=data.get("tenant_id", data.get("tenant_name", "")),
            agent_id=data.get("agent_id", data.get("agent_name", "")),
            event_type=event_type,
            event_hash=event_hash,
            timestamp_utc=data["timestamp_utc"],
            timestamp_local=data["timestamp_local"],
            tenant_name=data["tenant_name"],
            timezone_name=data.get("timezone_name", "America/New_York"),
            agent_name=data["agent_name"],
            agent_role=data.get("agent_role"),
            operator_id=data.get("operator_id"),
            workstation=data.get("workstation"),
            media_label=data.get("media_label"),
            uri=data.get("uri"),
            device_name=data.get("device_name"),
            busied_out_action=data.get("busied_out_action"),
            busied_out_duration=data.get("busied_out_duration"),
            reason_code=data.get("reason_code"),
            ring_group_name=data.get("ring_group_name"),
            ring_group_uri=data.get("ring_group_uri"),
            json_data=data.get("json_data", {}),
        )

    def to_dict(self) -> dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "tenant_id": self.tenant_id,
            "agent_id": self.agent_id,
            "event_type": self.event_type.value
            if isinstance(self.event_type, EventType)
            else self.event_type,
            "event_hash": self.event_hash,
            "timestamp_utc": self.timestamp_utc.isoformat()
            if isinstance(self.timestamp_utc, datetime)
            else self.timestamp_utc,
            "timestamp_local": self.timestamp_local.isoformat()
            if isinstance(self.timestamp_local, datetime)
            else self.timestamp_local,
            "tenant_name": self.tenant_name,
            "timezone_name": self.timezone_name,
            "agent_name": self.agent_name,
            "agent_role": self.agent_role,
            "operator_id": self.operator_id,
            "workstation": self.workstation,
            "media_label": self.media_label,
            "uri": self.uri,
            "device_name": self.device_name,
            "busied_out_action": self.busied_out_action,
            "busied_out_duration": self.busied_out_duration,
            "reason_code": self.reason_code,
            "ring_group_name": self.ring_group_name,
            "ring_group_uri": self.ring_group_uri,
            "json_data": self.json_data,
        }


@dataclass
class AgentEventBatch:
    """Represents a batch of agent events for bulk processing."""

    events: list[AgentEvent]
    batch_id: str
    received_at: datetime
    source_system: str = "sqs"

    def __post_init__(self) -> None:
        """Validate the batch."""
        if not self.events:
            raise ValueError("Batch cannot be empty")

        if len(self.events) > MAX_BATCH_SIZE:
            raise ValueError(f"Batch size cannot exceed {MAX_BATCH_SIZE} events")

    @property
    def size(self) -> int:
        """Get the batch size."""
        return len(self.events)

    @property
    def tenant_ids(self) -> set[str]:
        """Get unique tenant IDs in the batch."""
        return {event.tenant_id for event in self.events}

    @property
    def agent_ids(self) -> set[str]:
        """Get unique agent IDs in the batch."""
        return {event.agent_id for event in self.events}

    @property
    def event_types(self) -> set[EventType]:
        """Get unique event types in the batch."""
        return {event.event_type for event in self.events}

    def get_events_by_tenant(self, tenant_id: str) -> list[AgentEvent]:
        """Get events for a specific tenant."""
        return [event for event in self.events if event.tenant_id == tenant_id]

    def get_events_by_type(self, event_type: EventType) -> list[AgentEvent]:
        """Get events of a specific type."""
        return [event for event in self.events if event.event_type == event_type]
