"""Command for getting or creating a tenant."""

from dataclasses import dataclass


@dataclass(frozen=True)
class GetOrCreateTenantCommand:
    """Command to get existing tenant or create new one.

    Attributes:
        tenant_name: Tenant identifier
        tenant_display_name: Tenant display name (optional, defaults to tenant_name)
    """

    tenant_name: str
    tenant_display_name: str | None = None
