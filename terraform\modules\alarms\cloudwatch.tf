# CloudWatch alarm for Lambda function errors
# % Error Rate = (Errors / Invocations) * 100
resource "aws_cloudwatch_metric_alarm" "lambda_error_rate" {
  alarm_name                = "${var.lambda_function_name}-error-rate"
  comparison_operator       = "GreaterThanThreshold"
  evaluation_periods        = var.evaluation_periods
  datapoints_to_alarm       = max(1, ceil(var.evaluation_periods * 0.7))
  threshold                 = 1
  treat_missing_data        = "notBreaching"
  alarm_description         = "Lambda error rate for ${var.lambda_function_name}."
  alarm_actions             = ["arn:${data.aws_partition.current.partition}:sns:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${var.sns_topic_name}"]
  ok_actions                = ["arn:${data.aws_partition.current.partition}:sns:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${var.sns_topic_name}"]
  insufficient_data_actions = []
  metric_query {
    id = "m_errors"
    metric {
      namespace   = "AWS/Lambda"
      metric_name = "Errors"
      stat        = "Sum"
      period      = 300
      dimensions = {
        FunctionName = var.lambda_function_name
      }
    }
    return_data = false
  }

  metric_query {
    id = "m_invocations"
    metric {
      namespace   = "AWS/Lambda"
      metric_name = "Invocations"
      stat        = "Sum"
      period      = 300
      dimensions = {
        FunctionName = var.lambda_function_name
      }
    }
    return_data = false
  }

  metric_query {
    id          = "e1"
    label       = "Error rate (%)"
    expression  = "IF(m_invocations>0, (m_errors/m_invocations)*100, 0)"
    return_data = true
  }

  tags = merge(var.tags, {
    Name      = "${var.lambda_function_name}-error-rate"
    AlarmType = "LambdaErrorRate"
  })
}


# CloudWatch alarm for anomaly detection on Lambda invocations
resource "aws_cloudwatch_metric_alarm" "lambda_invocations_drop" {
  alarm_name                = "${var.lambda_function_name}-invocations-drop"
  comparison_operator       = "LessThanLowerThreshold"
  evaluation_periods        = var.evaluation_periods
  datapoints_to_alarm       = max(1, ceil(var.evaluation_periods * 0.7))
  treat_missing_data        = "notBreaching"
  threshold_metric_id       = "ad1"
  alarm_description         = "Detects a drop in invocations for ${var.lambda_function_name}."
  alarm_actions             = ["arn:${data.aws_partition.current.partition}:sns:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${var.sns_topic_name}"]
  ok_actions                = ["arn:${data.aws_partition.current.partition}:sns:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${var.sns_topic_name}"]
  insufficient_data_actions = []

  metric_query {
    id = "m1"
    metric {
      namespace   = "AWS/Lambda"
      metric_name = "Invocations"
      stat        = "Sum"
      period      = 300
      dimensions = {
        FunctionName = var.lambda_function_name
      }
    }
    return_data = true
  }

  metric_query {
    id          = "ad1"
    label       = "Invocations (expected)"
    expression  = "ANOMALY_DETECTION_BAND(m1, 2)"
    return_data = true
  }

  tags = merge(var.tags, {
    Name      = "${var.lambda_function_name}-invocations-drop"
    AlarmType = "LambdaInvocationsAnomaly"
  })
}


# CloudWatch alarm for Lambda function duration warnings
resource "aws_cloudwatch_metric_alarm" "lambda_duration_p95_warn" {
  alarm_name                            = "${var.lambda_function_name}-duration-p95-warn"
  comparison_operator                   = "GreaterThanThreshold"
  evaluation_periods                    = var.evaluation_periods
  treat_missing_data                    = "notBreaching"
  period                                = 300
  extended_statistic                    = "p95"
  evaluate_low_sample_count_percentiles = "ignore"
  threshold                             = var.lambda_timeout * 0.8 * 1000

  metric_name = "Duration"
  namespace   = "AWS/Lambda"

  dimensions = {
    FunctionName = var.lambda_function_name
  }

  alarm_description         = "p95 duration approaching timeout for ${var.lambda_function_name}."
  alarm_actions             = ["arn:${data.aws_partition.current.partition}:sns:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${var.sns_topic_name}"]
  ok_actions                = ["arn:${data.aws_partition.current.partition}:sns:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${var.sns_topic_name}"]
  insufficient_data_actions = []

  tags = merge(var.tags, {
    Name      = "${var.lambda_function_name}-duration-p95-warn"
    AlarmType = "LambdaDurationP95"
  })
}

# CloudWatch alarm for Lambda function throttles
resource "aws_cloudwatch_metric_alarm" "lambda_throttles" {
  alarm_name          = "${var.lambda_function_name}-throttles"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = var.evaluation_periods
  datapoints_to_alarm = max(1, ceil(var.evaluation_periods * 0.7))
  treat_missing_data  = "notBreaching"
  threshold           = 1
  metric_name         = "Throttles"
  namespace           = "AWS/Lambda"
  period              = 300
  statistic           = "Sum"
  dimensions = {
    FunctionName = var.lambda_function_name
  }
  alarm_description         = "Lambda throttles detected for ${var.lambda_function_name}."
  alarm_actions             = ["arn:${data.aws_partition.current.partition}:sns:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${var.sns_topic_name}"]
  ok_actions                = ["arn:${data.aws_partition.current.partition}:sns:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${var.sns_topic_name}"]
  insufficient_data_actions = []

  tags = merge(var.tags, {
    Name      = "${var.lambda_function_name}-throttles"
    AlarmType = "LambdaThrottles"
  })
}
