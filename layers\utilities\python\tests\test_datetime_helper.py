"""Tests for datetime helper utilities."""

from datetime import datetime

from smartanalytics_utilities.utils.datetime_helper import (
    get_client_timezone,
    get_current_time,
)


class TestDatetimeHelper:
    """Test cases for datetime helper functions."""

    def test_get_current_time(self):
        """Test getting current UTC time."""
        dt = get_current_time()
        assert dt.tzinfo is not None
        assert isinstance(dt, datetime)

    def test_get_client_timezone(self):
        """Test getting client timezone."""
        tz = get_client_timezone("America/Chicago")
        assert tz == "America/Chicago"

        # Test default
        default_tz = get_client_timezone()
        assert isinstance(default_tz, str)

    def test_get_client_timezone_with_different_zones(self):
        """Test getting client timezone with various timezone strings."""
        timezones = [
            "America/New_York",
            "America/Los_Angeles",
            "Europe/London",
            "Asia/Tokyo",
        ]

        for tz in timezones:
            result = get_client_timezone(tz)
            assert result == tz
