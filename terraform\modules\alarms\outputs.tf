# CloudWatch alarm outputs
output "lambda_errors_alarm_name" {
  description = "Name of the Lambda errors rate alarm"
  value       = aws_cloudwatch_metric_alarm.lambda_error_rate.alarm_name
}

output "lambda_errors_alarm_arn" {
  description = "ARN of the Lambda errors rate alarm"
  value       = aws_cloudwatch_metric_alarm.lambda_error_rate.arn
}

output "lambda_invocations_alarm_name" {
  description = "Name of the Lambda invocations drop alarm"
  value       = aws_cloudwatch_metric_alarm.lambda_invocations_drop.alarm_name
}

output "lambda_invocations_alarm_arn" {
  description = "ARN of the Lambda invocations drop alarm"
  value       = aws_cloudwatch_metric_alarm.lambda_invocations_drop.arn
}

output "lambda_duration_alarm_name" {
  description = "Name of the Lambda duration warning alarm"
  value       = aws_cloudwatch_metric_alarm.lambda_duration_p95_warn.alarm_name
}

output "lambda_duration_alarm_arn" {
  description = "ARN of the Lambda duration warning alarm"
  value       = aws_cloudwatch_metric_alarm.lambda_duration_p95_warn.arn
}

output "lambda_throttles_alarm_name" {
  description = "Name of the Lambda throttles alarm"
  value       = aws_cloudwatch_metric_alarm.lambda_throttles.alarm_name
}

output "lambda_throttles_alarm_arn" {
  description = "ARN of the Lambda throttles alarm"
  value       = aws_cloudwatch_metric_alarm.lambda_throttles.arn
}
