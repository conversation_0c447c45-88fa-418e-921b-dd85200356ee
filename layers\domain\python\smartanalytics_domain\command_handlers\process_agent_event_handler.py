"""Command handler for processing a single agent event."""

import time
from typing import Any

from smartanalytics_domain.command_handlers.get_or_create_agent_handler import (
    GetOrCreateAgentCommandHandler,
)
from smartanalytics_domain.command_handlers.get_or_create_ring_group_handler import (
    GetOrCreateRingGroupCommandHandler,
)
from smartanalytics_domain.command_handlers.get_or_create_tenant_handler import (
    GetOrCreateTenantCommandHandler,
)
from smartanalytics_domain.commands.get_or_create_agent_command import (
    GetOrCreateAgentCommand,
)
from smartanalytics_domain.commands.get_or_create_ring_group_command import (
    GetOr<PERSON>reateRingGroupCommand,
)
from smartanalytics_domain.commands.get_or_create_tenant_command import (
    GetOrCreateTenantCommand,
)
from smartanalytics_domain.commands.process_agent_event_command import (
    ProcessAgentEventCommand,
)
from smartanalytics_domain.exceptions.database_exceptions import (
    Duplicate<PERSON><PERSON>ord<PERSON>rror,
    IntegrityConstraintError,
)
from smartanalytics_domain.models.agent_event import Agent<PERSON><PERSON>
from smartanalytics_domain.models.processing_result import SingleEventResult
from smartanalytics_domain.ports.unit_of_work import UnitOfWork
from smartanalytics_utilities.utils.datetime_helper import (
    convert_to_tenant_timezone,
    parse_iso_datetime,
)
from smartanalytics_utilities.utils.event_helper import extract_nested_event_data
from smartanalytics_utilities.utils.logging_helper import get_logger
from smartanalytics_utilities.utils.xml_helper import ACDEventXMLParser

# Note: IntegrityError handling is done via IntegrityConstraintError domain exception
# to avoid SQLAlchemy dependency in domain layer

logger = get_logger(__name__)


class ProcessAgentEventCommandHandler:
    """Handler for processing a single agent event command.

    This handler orchestrates the complete flow of processing an agent event:
    1. Parse XML to JSON
    2. Extract and convert timestamps
    3. Extract nested event-specific data
    4. Create domain model
    5. Get or create dimension records (using sub-commands)
    6. Save event to database (with duplicate detection via unique constraint)

    This handler demonstrates command composition - it calls other command handlers
    for dimension management (tenant, agent, ring group).
    """

    def __init__(self, uow: UnitOfWork):
        """Initialize handler with unit of work.

        Args:
            uow: Unit of work for database operations
        """
        self.uow = uow
        # Initialize sub-command handlers
        self.tenant_handler = GetOrCreateTenantCommandHandler(uow)
        self.agent_handler = GetOrCreateAgentCommandHandler(uow)
        self.ring_group_handler = GetOrCreateRingGroupCommandHandler(uow)

    async def handle(self, command: ProcessAgentEventCommand) -> SingleEventResult:
        """Handle the process agent event command.

        Args:
            command: Command containing XML content and metadata

        Returns:
            SingleEventResult with processing outcome

        Raises:
            Exception: If critical processing error occurs
        """
        start_time = time.time()

        try:
            # 1. Parse XML to JSON
            json_data = ACDEventXMLParser.xml_to_json(command.xml_content)

            # 3. Create domain model
            agent_event = self._convert_to_agent_event(
                json_data, command.client_timezone
            )

            # 4. Try to save event (duplicate detection via unique constraint)
            async with self.uow:
                try:
                    # Get or create tenant (using sub-command)
                    tenant_command = GetOrCreateTenantCommand(
                        tenant_name=agent_event.tenant_name,
                        tenant_display_name=agent_event.tenant_name,
                    )
                    tenant_key = await self.tenant_handler.handle(tenant_command)

                    # Get or create agent (using sub-command)
                    agent_command = GetOrCreateAgentCommand(
                        tenant_key=tenant_key,
                        agent_name=agent_event.agent_name,
                        agent_role=agent_event.agent_role,
                    )
                    agent_key = await self.agent_handler.handle(agent_command)

                    # Get or create ring group (if applicable, using sub-command)
                    ring_group_key: int | None = None
                    if agent_event.has_ring_group:
                        ring_group_command = GetOrCreateRingGroupCommand(
                            tenant_key=tenant_key,
                            ring_group_name=agent_event.ring_group_name or "",
                            ring_group_uri=agent_event.ring_group_uri or "",
                        )
                        ring_group_key = await self.ring_group_handler.handle(
                            ring_group_command
                        )

                    # Prepare dimension keys (ring_group_key can be None)
                    dimension_keys: dict[str, int | None] = {
                        "tenant_key": tenant_key,
                        "agent_key": agent_key,
                        "ring_group_key": ring_group_key,
                    }

                    # Save event
                    event_key = await self.uow.agent_events.save(
                        agent_event, dimension_keys
                    )
                    await self.uow.commit()

                    processing_time = (time.time() - start_time) * 1000

                    logger.info(
                        "ACD event processed successfully",
                        event_hash=agent_event.event_hash,
                        event_type=agent_event.event_type.value,
                        agent=agent_event.agent_name,
                        tenant=agent_event.tenant_name,
                        event_key=event_key,
                        processing_time_ms=processing_time,
                    )

                    return SingleEventResult(
                        success=True,
                        event_hash=agent_event.event_hash,
                        event_type=agent_event.event_type.value,
                        is_duplicate=False,
                        processing_time_ms=processing_time,
                    )

                except DuplicateRecordError:
                    # Handle domain-level duplicate record error
                    await self.uow.rollback()

                    processing_time = (time.time() - start_time) * 1000

                    logger.info(
                        "Duplicate event detected, skipping",
                        event_hash=agent_event.event_hash,
                        event_type=agent_event.event_type.value,
                        agent=agent_event.agent_name,
                        tenant=agent_event.tenant_name,
                        processing_time_ms=processing_time,
                    )

                    return SingleEventResult(
                        success=True,
                        event_hash=agent_event.event_hash,
                        event_type=agent_event.event_type.value,
                        is_duplicate=True,
                        processing_time_ms=processing_time,
                    )

                except IntegrityConstraintError as e:
                    # Check if it's a duplicate event (unique constraint violation)
                    if (
                        "uq_agent_event_hash" in str(e).lower()
                        or "duplicate key" in str(e).lower()
                    ):
                        await self.uow.rollback()

                        processing_time = (time.time() - start_time) * 1000

                        logger.info(
                            "Duplicate event detected, skipping",
                            event_hash=agent_event.event_hash,
                            event_type=agent_event.event_type.value,
                            agent=agent_event.agent_name,
                            tenant=agent_event.tenant_name,
                            processing_time_ms=processing_time,
                        )

                        return SingleEventResult(
                            success=True,
                            event_hash=agent_event.event_hash,
                            event_type=agent_event.event_type.value,
                            is_duplicate=True,
                            processing_time_ms=processing_time,
                        )
                    # Different integrity error - re-raise
                    raise DuplicateRecordError(
                        "Duplicate record detected",
                        context={"event_hash": agent_event.event_hash},
                    ) from e

        except Exception as e:
            processing_time = (time.time() - start_time) * 1000

            logger.error(
                "Failed to process ACD event",
                error=str(e),
                error_type=type(e).__name__,
                processing_time_ms=processing_time,
            )

            return SingleEventResult(
                success=False,
                event_hash="",
                event_type="",
                is_duplicate=False,
                processing_time_ms=processing_time,
                error_message=str(e),
            )

    def _convert_to_agent_event(
        self, json_data: dict[str, Any], client_timezone: str
    ) -> AgentEvent:
        """Convert parsed JSON to agent event data with timezone conversion.

        Args:
            json_data: Parsed JSON data from XML
            client_timezone: Client timezone for conversion

        Returns:
            Dictionary with agent event data ready for domain model
        """
        # Parse timestamp
        timestamp_utc = parse_iso_datetime(json_data["timestamp"])
        timestamp_local = convert_to_tenant_timezone(timestamp_utc, client_timezone)

        # Extract nested event-specific data
        nested_data = extract_nested_event_data(json_data, json_data["eventType"])

        # Build event data
        event_data = {
            "tenant_name": json_data["agencyOrElement"],
            "agent_name": json_data["agent"],
            "event_type": json_data["eventType"],
            "timestamp_utc": timestamp_utc,
            "timestamp_local": timestamp_local,
            "timezone_name": client_timezone,
            "json_data": json_data,
            # Nested fields
            "agent_role": nested_data.get("agentRole"),
            "operator_id": nested_data.get("operatorId"),
            "workstation": nested_data.get("workstation"),
            "media_label": nested_data.get("mediaLabel"),
            "uri": nested_data.get("uri") or nested_data.get("agentUri"),
            "device_name": nested_data.get("deviceName"),
            "ring_group_name": nested_data.get("ringGroupName"),
            "ring_group_uri": nested_data.get("ringGroupUri"),
            "busied_out_action": nested_data.get("action"),
            "busied_out_duration": (
                int(duration) if (duration := nested_data.get("duration")) else None
            ),
            "reason_code": nested_data.get("reason") or nested_data.get("responseCode"),
        }

        return AgentEvent.from_dict(event_data)
