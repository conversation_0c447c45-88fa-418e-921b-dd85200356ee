# SmartAnalytics Infrastructure Layer

This package contains the infrastructure implementations for the SmartAnalytics system.

## Features

- SQLAlchemy database models
- Repository implementations
- Database connection management
- Unit of work pattern implementation

## Installation

```bash
pip install -e .
```

## Usage

```python
from smartanalytics_infrastructure.database.models import DimAgentEvent
from smartanalytics_infrastructure.repositories.agent_event_repository import SqlAlchemyAgentEventRepository
```
