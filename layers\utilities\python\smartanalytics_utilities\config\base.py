"""Base configuration classes and enums."""

from enum import Enum
from typing import Any

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Environment(str, Enum):
    """Application environment enumeration."""

    DEV = "dev"
    QA = "qa"
    PROD = "prod"
    LOCAL = "local"


class BaseConfig(BaseSettings):
    """Base configuration class with common settings."""

    environment: Environment = Field(
        default=Environment.DEV, description="Application environment"
    )

    debug: bool = Field(default=False, description="Enable debug mode")

    service_name: str = Field(
        default="smartanalytics", description="Service name for logging and metrics"
    )

    version: str = Field(default="1.0.0", description="Application version")

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        validate_assignment=True,
        extra="ignore",
        str_strip_whitespace=True,
    )

    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment == Environment.PROD

    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment in (Environment.DEV, Environment.LOCAL)

    def get_log_level(self) -> str:
        """Get appropriate log level for environment."""
        if self.debug or self.is_development():
            return "DEBUG"
        if self.environment == Environment.QA:
            return "INFO"
        return "WARNING"

    def to_dict(self) -> dict[str, Any]:
        """Convert configuration to dictionary."""
        return self.model_dump()

    def __repr__(self) -> str:
        """String representation of configuration."""
        return f"{self.__class__.__name__}(environment={self.environment.value})"
