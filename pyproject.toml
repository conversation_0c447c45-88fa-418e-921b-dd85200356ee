# Smart Analytics Processors - Top-level Python Tool Configuration
# This file provides shared configuration for all Python lambdas in the repository

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

# Ruff configuration is in ruff.toml for better organization
# This section is kept minimal to avoid duplication

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

# Import discovery
namespace_packages = true
explicit_package_bases = true

# Error output
show_column_numbers = true
show_error_context = true
color_output = true
error_summary = true

exclude = '''(?x)
  ^build/ |
  ^dist/ |
  ^\.venv/ |
  .*/build/ |
  .*/dist/ |
  .*/tests/ |
  ^layers/.*/python/tests/ |
  ^lambdas/.*/tests/ |
  ^layers/domain/python/__init__\.py$ |
  ^lambdas/acd-event-processor/src/__init__\.py$
'''


# Ignore missing imports for third-party libraries
ignore_missing_imports = false
mypy_path = [
  "lambdas/acd-event-processor/src",
  "layers/utilities/python",
  "layers/infrastructure/python",
  "layers/domain/python"
]

[[tool.mypy.overrides]]
module = [
    "psycopg2.*",
    "aws_xray_sdk.*",
    "pytz.*",
    "pydantic.*",
    "pydantic_settings.*",
    "xmltodict.*",
    "smartanalytics_utilities.*",
    "smartanalytics_infrastructure.*",
    "smartanalytics_domain.*",
    "sqlalchemy.*"
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --cov=src --cov=layers --cov-report=term-missing --cov-report=html --cov-report=xml --cov-fail-under=70 --import-mode=importlib"
testpaths = [
    "lambdas/acd-event-processor/tests",
    "layers/utilities/python/tests",
    "layers/domain/python/tests",
    "layers/infrastructure/python/tests"
]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "slow: Slow running tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "test_*", ".venv", "venv", "build", "dist"]
skips = ["B101", "B104", "B106", "B108", "B311"]  # Allow assert, hardcoded passwords in tests, etc.
