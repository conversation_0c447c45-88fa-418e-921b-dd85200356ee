# Development dependencies for ACD Event Processor Lambda
# These dependencies are used for testing, code quality, and development

# Testing framework
pytest==7.4.4
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-asyncio==0.23.2

# Environment management
python-dotenv==1.0.0

# Code quality and formatting
ruff==0.8.4
mypy==1.8.0
bandit==1.7.5

# Type stubs for better type checking
types-xmltodict==0.15.0.20250907

# Development utilities
coverage==7.4.1

# Layer dependencies for local development
# Note: In production, these come from Lambda layers
pydantic==2.11.1
pydantic-settings==2.10.1
aws-lambda-powertools[all]==3.13.0
xmltodict==0.13.0
pytz==2023.3
python-dateutil==2.8.2
asyncpg==0.30.0
psycopg2-binary==2.9.10
sqlalchemy[asyncio]==2.0.25
tenacity==8.2.3
