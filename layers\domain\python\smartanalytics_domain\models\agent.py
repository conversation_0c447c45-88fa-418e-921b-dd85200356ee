"""Agent domain entity."""

from dataclasses import dataclass
from datetime import datetime


@dataclass(frozen=True)
class Agent:
    """Agent domain entity representing a call center agent.

    This is an immutable value object representing an agent in the system.
    """

    agent_key: int
    tenant_key: int
    agent_name: str
    agent_role: str | None
    created_at: datetime

    def __post_init__(self) -> None:
        """Validate agent data."""
        if not self.agent_name:
            raise ValueError("Agent name cannot be empty")
        if self.tenant_key <= 0:
            raise ValueError("Tenant key must be positive")
