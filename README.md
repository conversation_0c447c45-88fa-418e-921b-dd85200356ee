# SmartAnalytics Agent Event Processor

A high-performance, serverless agent event processing system built with **hexagonal architecture** and **Lambda layers**, designed for Aurora PostgreSQL with improved performance and cost efficiency.

## Architecture Overview

This project implements a **hexagonal architecture** (ports and adapters pattern) with clean separation of concerns:


```mermaid
graph TD
    A[SQS Queue] --> B[Lambda Functions]
    B --> C[XML Parser]
    C --> D[Domain Layer]
    D --> E[Command Handlers]
    E --> F[Repository Layer]
    F --> G[RDS Database]

    H[CloudWatch] --> I[Monitoring]
    B --> H

    J[IAM Roles] --> B
```

## Key Features

### Performance Improvements
- **5-10x faster writes** compared to traditional data warehouse solutions
- **Sub-100ms latency** for event processing
- **Bulk insert optimization** with PostgreSQL `ON CONFLICT DO NOTHING`
- **Connection pooling** via Aurora PostgreSQL for Lambda functions
- **2000x smaller deployments** with Lambda layers (10 KB vs 20 MB packages)

### Architecture Benefits
- **Clean separation of concerns** with hexagonal architecture
- **Testable business logic** isolated from infrastructure
- **Type-safe code** with strong typing throughout
- **Async processing** with SQLAlchemy 2.0 and asyncpg
- **Lambda container optimization** with connection reuse
- **Lambda layers** for shared code and dependency management
  - **Utilities layer**: Configuration, logging, datetime/hash helpers (~15 MB)
  - **Domain layer**: Business logic, commands, handlers (~50 KB - pure Python)
  - **Infrastructure layer**: Database, repositories, SQLAlchemy (~4 MB)
  - **Small Lambda packages**: Handler code only (~10 KB vs ~20 MB without layers)
  - **Automatic CI/CD**: Layers built and deployed automatically in pipeline

### Data Processing
- **Efficient duplicate handling** using unique constraints
- **Bulk operations** for high-throughput processing
- **SCD Type 2** support for dimensional data
- **Event hashing** for deduplication (SHA-256)
- **Comprehensive error handling** with domain exceptions

## Platform Features

### Multi-Customer Architecture
- Isolated processing per customer with dedicated resources
- Customer-specific configurations and environment variables
- Scalable design supporting unlimited customers

### Security
- VPC deployment with private subnet isolation
- IAM-based authentication with least privilege access
- Comprehensive audit logging and compliance features

### Operational
- Infrastructure as Code with Terraform
- Automated CI/CD pipelines with GitLab
- Comprehensive monitoring and alerting
- Production-ready error handling and recovery

## Project Structure

```
smartanalytics-processors/
├── layers/                          # Lambda Layers (shared code & dependencies)
│   ├── utilities/                   # Utilities layer (pydantic, powertools, helpers)
│   │   ├── python/smartanalytics_utilities/
│   │   │   ├── config/             # Configuration management
│   │   │   ├── datetime_helper.py  # Timezone & datetime utilities
│   │   │   ├── hash_helper.py      # Event hashing utilities
│   │   │   ├── logging_helper.py   # Logging configuration
│   │   │   └── xml_helper.py       # XML parsing utilities
│   │   ├── requirements.txt        # Layer dependencies
│   │   └── README.md               # Utilities layer documentation
│   ├── domain/                     # Domain layer (business logic)
│   │   ├── python/smartanalytics_domain/
│   │   │   ├── commands/           # Command objects
│   │   │   ├── command_handlers/   # Command handlers (CQRS)
│   │   │   ├── models/             # Domain models
│   │   │   ├── ports/              # Repository interfaces
│   │   │   └── exceptions/         # Domain exceptions
│   │   ├── requirements.txt        # No external dependencies (pure Python)
│   │   └── README.md               # Domain layer documentation
│   └── infrastructure/             # Infrastructure layer (database, repos)
│       ├── python/smartanalytics_infrastructure/
│       │   ├── database/           # Database connection & session
│       │   ├── models/             # SQLAlchemy ORM models
│       │   ├── repositories/       # Repository implementations
│       │   └── unit_of_work.py     # Unit of Work pattern
│       ├── requirements.txt        # asyncpg, sqlalchemy, psycopg2
│       └── README.md               # Infrastructure layer documentation
│
├── lambdas/                         # Lambda Functions
│   ├── README.md                    # Lambda functions overview
│   └── acd-event-processor/         # ACD event processor Lambda
│       ├── src/
│       │   └── lambda_function.py   # Lambda handler (minimal - uses layers)
│       ├── tests/                   # Lambda-specific tests
│       │   ├── test_lambda_handler.py
│       │   └── conftest.py          # Test configuration
│       ├── integration_test.py      # Integration test script
│       ├── requirements.txt         # Lambda-only dependencies (minimal)
│       ├── requirements-dev.txt     # Development dependencies
│       └── README.md                # Lambda function documentation
│
├── terraform/                       # Infrastructure as Code
│   ├── modules/
│   │   └── acd-processor/           # ACD processor module
│   │       ├── lambda.tf            # Lambda function configuration
│   │       ├── layers.tf            # Lambda layer configuration
│   │       ├── iam.tf               # IAM roles and policies
│   │       ├── security_group.tf    # VPC security groups
│   │       ├── variables.tf         # Module variables
│   │       ├── outputs.tf           # Module outputs
│   │       └── README.md            # Module documentation
│   ├── main.tf                      # Root Terraform configuration
│   ├── variables.tf                 # Root variables
│   ├── outputs.tf                   # Root outputs
│   ├── terraform.tfvars.example     # Example configuration
│   └── README.md                    # Terraform documentation
│
├── pyproject.toml                   # Python project configuration
├── requirements-ci.txt              # CI/CD dependencies
├── ruff.toml                        # Ruff linter configuration
├── CONFIGURATION_GUIDE.md           # Configuration guide
├── CHANGELOG.md                     # Change log
└── README.md                        # This file
```

## Getting Started

### Quick Navigation
- **[Lambda Functions](lambdas/README.md)** - Overview of all processing functions
- **[Infrastructure](terraform/README.md)** - Deployment and infrastructure management
- **[Development Guide](CONFIGURATION_GUIDE.md)** - Setup and development standards

### Prerequisites
- Python 3.12+
- AWS CLI configured with appropriate permissions
- Terraform 1.6+ for infrastructure deployment
- Access to target AWS account and Aurora PostgreSQL cluster

### Local Development Setup

1. **Clone and setup environment**
   ```bash
   git clone <repository-url>
   cd smartanalytics-processors
   ```

2. **Choose a Lambda function to work on**
   ```bash
   cd lambdas/agent-event-processor
   python -m venv .venv
   source .venv/bin/activate  # Windows: .venv\Scripts\activate
   pip install -r requirements-dev.txt
   ```

3. **Install development tools**
   ```bash
   pre-commit install
   ```

4. **Verify setup**
   ```bash
   python -m pytest tests/unit/ -v
   python -m ruff check .
   python -m mypy src
   ```

## Technology Stack

### Core Platform
- **AWS Lambda**: Serverless compute with Python 3.12
- **Amazon SQS**: Event queuing and batch processing
- **Aurora PostgreSQL**: High-performance managed database
- **Terraform**: Infrastructure as Code

### Development Standards
- **Ruff**: Code formatting and linting
- **MyPy**: Static type checking
- **Pytest**: Testing framework
- **Pre-commit**: Quality gate automation

### Operational
- **GitLab CI/CD**: Automated testing and deployment
- **CloudWatch**: Monitoring and alerting
- **VPC**: Network security and isolation
- **IAM**: Least privilege access control

## Architecture Principles

### Scalability
- Serverless-first design for automatic scaling
- Event-driven architecture with SQS queuing
- Multi-customer isolation with dedicated resources

### Reliability
- Idempotent processing with duplicate detection
- Dead letter queues for error handling
- Comprehensive monitoring and alerting

### Security
- VPC deployment with private subnet isolation
- IAM-based authentication for all services
- Encryption in transit and at rest

### Maintainability
- Infrastructure as Code with Terraform
- Comprehensive test coverage and quality gates
- Structured logging and observability

## Project Components

### Lambda Functions
Each Lambda function is self-contained with its own:
- Source code and dependencies
- Test suites and quality checks
- Documentation and examples
- Database scripts and schemas

### Infrastructure Modules
Terraform modules provide:
- Reusable infrastructure patterns
- Environment-specific configurations
- Monitoring and alerting setup
- Security and compliance controls

### Development Tools
Repository-wide standards for:
- Code quality and formatting
- Testing and coverage requirements
- Security scanning and compliance
- CI/CD pipeline automation

## Support

### Documentation
- Component-specific README files for detailed information
- Architecture diagrams and data flow documentation
- Troubleshooting guides and common solutions

### Development
- GitLab issues for bug reports and feature requests
- Code review process for quality assurance
- Automated testing and quality gates

### Operations
- CloudWatch monitoring and alerting
- Automated deployment pipelines
- Infrastructure drift detection and remediation
