# ACD Event Processor Lambda Configuration

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "acd-event-processor"
version = "1.0.0"
description = "AWS Lambda function for processing ACD agent events"
authors = [
    {name = "SmartAnalytics Team"}
]
readme = "README.md"
requires-python = ">=3.12"
classifiers = [
    "Intended Audience :: Developers",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    "aws-lambda-powertools[all]>=2.30.0",
    "asyncpg>=0.29.0",
    "psycopg2-binary>=2.9.9",
    "sqlalchemy[asyncio]>=2.0.23",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "aiofiles>=23.2.1",
    "pytz>=2023.3",
    "orjson>=3.9.10",
    "tenacity>=8.2.3",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "moto[sqs]>=4.2.0",
    "httpx>=0.25.0",
    "ruff>=0.1.0",
    "mypy>=1.7.0",
    "bandit>=1.7.5",
    "pre-commit>=3.5.0",
]

test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "moto[sqs]>=4.2.0",
    "httpx>=0.25.0",
]
