variable "region" {
  type        = string
  description = "AWS Region to use for deployment.  Defaults to 'us-east-1'."
  default     = "us-east-1"
}

variable "environment" {
  type        = string
  description = "Environment to deploy to.  Defaults to 'dev'."
  default     = "dev"
  validation {
    condition     = contains(["dev", "qa", "prod"], var.environment)
    error_message = "environment must be one of 'dev', 'qa', 'prod'."
  }
}

variable "country" {
  type        = string
  description = "Country to deploy to.  Defaults to 'us'."
  default     = "us"
  validation {
    condition     = contains(["us", "ca", "au"], var.country)
    error_message = "country must be one of 'us', 'ca', 'au'."
  }
}

variable "global_tags" {
  type        = map(string)
  description = "Map of key/value string pairs to apply to all resources as tags."
}

variable "slack_errors_alarm_topic_name" {
  type        = string
  description = "Name of the SNS topic for data lake CloudWatch alarms."
}

variable "project_dir" {
  description = "Full path to working directory passed from GitLab CI"
  type        = string
  nullable    = false
}

variable "service_customer_config" {
  type = map(object({
    # S3 bucket name for lambda code.
    s3_lambda_code_bucket_name = string

    # KMS key name for lambda code.
    kms_key_name = string

    acd = object({
      # Enable ACD lambda
      enable = bool

      # Lambda zip file name.
      lambda_zip_file_name = string

      # SQS queue name for lambda trigger.
      queue_name = string

      # SQS dead letter queue name for lambda trigger.
      dlq_name = string

      # List of subnet IDs to associate with the Lambda function.
      subnet_ids = list(string)

      # VPC ID where the Lambda function will be deployed
      vpc_id = string

      # Database secret name in AWS Secrets Manager.
      database_secret_name = string

      # RDS cluster identifier
      rds_cluster_identifier = string

      # RDS cluster resource ID
      rds_cluster_resource_id = string

      # Database name
      database_name = string

      # Database user name
      database_user_name = string

      # Environment variables
      environment_variables = map(string)
    })

    # List of key/value pairs for tags.
    tags = map(string)
  }))
  description = "Customer configuration."
}
