[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "smartanalytics-infrastructure"
version = "1.0.0"
description = "Infrastructure layer for SmartAnalytics applications"
authors = [
    {name = "Smart Analytics Team"}
]
readme = "README.md"
requires-python = ">=3.12"
classifiers = [
    "Intended Audience :: Developers",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    # Core dependencies
    "pydantic>=2.5.0",

    # Database dependencies
    "sqlalchemy[asyncio]>=2.0.36",
    "asyncpg>=0.29.0",
    "psycopg2-binary>=2.9.9",

    # AWS dependencies
    "boto3>=1.34.0",
    "botocore>=1.34.0",

    # Layer dependencies
    "smartanalytics-utilities",
    "smartanalytics-domain",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",

    # Code quality
    "black>=23.12.0",
    "ruff>=0.1.9",
    "mypy>=1.8.0",
    "bandit>=1.7.5",

    # Type stubs
    "types-psycopg2>=2.9.21",
    "boto3-stubs[essential]>=1.34.0",
]
