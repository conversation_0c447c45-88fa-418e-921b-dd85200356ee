"""XML parsing utilities for SmartAnalytics applications.

This module provides secure XML parsing functionality for converting
XML events to structured data while maintaining security best practices.
"""

import json
from typing import Any, ClassVar
from xml.parsers import expat as expat_parser

import xmltodict
from aws_lambda_powertools import Logger

logger = Logger(service="smartanalytics-xml-parser")


class XMLParser:
    """Secure XML parser for SmartAnalytics events."""

    @classmethod
    def xml_to_json(cls, xml_content: str) -> dict[str, Any]:
        """Convert XML content to JSON object with security features.

        Args:
            xml_content: Raw XML content

        Returns:
            Clean JSON object with hierarchy preserved

        Raises:
            ValueError: If XML parsing fails or content is invalid
        """
        try:
            # Convert XML to dictionary using xmltodict with enhanced security
            data = xmltodict.parse(
                xml_content,
                process_namespaces=False,  # Disable namespace processing for security
                namespace_separator=":",
                disable_entities=True,  # Disable entity processing for security
                expat=expat_parser,  # Use expat parser for better security
                process_comments=False,  # Disable comment processing
                strip_whitespace=True,  # Strip whitespace for cleaner data
            )

            # Extract the LogEvent content (remove root wrapper if present)
            json_data = data.get("LogEvent", data)

            logger.debug(
                "Successfully converted XML to JSON",
                data_keys=list(json_data.keys())
                if isinstance(json_data, dict)
                else "non-dict",
            )

            result: dict[str, Any] = (
                dict(json_data) if isinstance(json_data, dict) else {}
            )
            return result

        except Exception as e:
            logger.error(
                "XML to JSON conversion failed",
                error=str(e),
                xml_preview=xml_content[:500] if xml_content else "empty",
            )
            raise ValueError(f"Failed to parse XML: {e}") from e

    @classmethod
    def extract_xml_from_sqs_message(cls, sqs_body: str | dict[str, Any]) -> str:
        """Extract XML content from SQS message body.

        The SQS message might be wrapped in JSON (SNS->SQS) or contain raw XML.

        Args:
            sqs_body: Raw SQS message body (string or dict)

        Returns:
            Extracted XML content

        Raises:
            ValueError: If XML content cannot be extracted or is invalid
        """
        # Handle case where sqs_body is already a dict (from test payloads)
        if isinstance(sqs_body, dict):
            if "Message" in sqs_body:
                xml_content = sqs_body["Message"]
            else:
                raise ValueError("Dict body must contain 'Message' key")
        else:
            # Handle string body
            try:
                # Try to parse as JSON first (SNS -> SQS pattern)
                message_data = json.loads(sqs_body)

                # Check if it's an SNS message
                xml_content = message_data.get("Message", sqs_body)

            except json.JSONDecodeError:
                # Raw XML content
                xml_content = sqs_body

        # Additional validation
        if not isinstance(xml_content, str):
            raise ValueError(f"XML content must be string, got {type(xml_content)}")

        xml_content = xml_content.strip()
        if not xml_content:
            raise ValueError("Empty XML content after extraction")

        return xml_content

    @classmethod
    def validate_xml_structure(
        cls, json_data: dict[str, Any], required_fields: list[str]
    ) -> None:
        """Validate that JSON data contains required fields.

        Args:
            json_data: Parsed JSON data from XML
            required_fields: List of required field names

        Raises:
            ValueError: If required fields are missing
        """
        if not isinstance(json_data, dict):
            raise ValueError("JSON data must be a dictionary")

        missing_fields = [f for f in required_fields if f not in json_data]
        if missing_fields:
            raise ValueError(f"Missing required fields: {missing_fields}")


class AgentEventXMLParser(XMLParser):
    """Specialized XML parser for agent events."""

    REQUIRED_FIELDS: ClassVar[list[str]] = [
        "timestamp",
        "agencyOrElement",
        "agent",
        "eventType",
    ]

    @classmethod
    def xml_to_json(cls, xml_content: str) -> dict[str, Any]:
        """Convert agent event XML to JSON with validation.

        Args:
            xml_content: Raw XML content from SQS message

        Returns:
            Clean JSON object with hierarchy preserved

        Raises:
            ValueError: If XML parsing fails or required fields are missing
        """
        json_data = super().xml_to_json(xml_content)

        # Validate required fields for agent events
        cls.validate_xml_structure(json_data, cls.REQUIRED_FIELDS)

        logger.info(
            "Successfully converted agent event XML to JSON",
            event_type=json_data.get("eventType"),
            agent=json_data.get("agent"),
            agency=json_data.get("agencyOrElement"),
        )

        return json_data


class ACDEventXMLParser(XMLParser):
    """Specialized XML parser for ACD events."""

    REQUIRED_FIELDS: ClassVar[list[str]] = [
        "timestamp",
        "agencyOrElement",
        "agent",
        "eventType",
    ]

    @classmethod
    def xml_to_json(cls, xml_content: str) -> dict[str, Any]:
        """Convert ACD event XML to JSON with validation.

        Args:
            xml_content: Raw XML content from SQS message

        Returns:
            Clean JSON object with hierarchy preserved

        Raises:
            ValueError: If XML parsing fails or required fields are missing
        """
        json_data = super().xml_to_json(xml_content)

        # Validate required fields for ACD events
        cls.validate_xml_structure(json_data, cls.REQUIRED_FIELDS)

        logger.info(
            "Successfully converted ACD event XML to JSON",
            event_type=json_data.get("eventType"),
            agent=json_data.get("agent"),
            agency=json_data.get("agencyOrElement"),
        )

        return json_data
