# Lambda Layer Terraform Module

This module creates an AWS Lambda Layer from a zip file stored in S3.

## Usage

```hcl
module "utilities_layer" {
  source = "../lambda-layer"

  layer_name       = "smartanalytics-utilities"
  description      = "SmartAnalytics utilities layer with shared helpers"
  s3_bucket        = aws_s3_object.utilities_layer.bucket
  s3_key           = aws_s3_object.utilities_layer.key
  source_code_hash = filebase64sha256("${var.project_dir}/build/utilities-layer.zip")
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| layer_name | Name of the Lambda layer | `string` | n/a | yes |
| description | Description of the Lambda layer | `string` | n/a | yes |
| s3_bucket | S3 bucket containing the layer code | `string` | n/a | yes |
| s3_key | S3 key for the layer zip file | `string` | n/a | yes |
| source_code_hash | Hash of the layer source code | `string` | n/a | yes |
| compatible_runtimes | Compatible Lambda runtimes | `list(string)` | `["python3.12"]` | no |
| compatible_architectures | Compatible architectures | `list(string)` | `["x86_64"]` | no |
| skip_destroy | Skip destroying layer on terraform destroy | `bool` | `false` | no |

## Outputs

| Name | Description |
|------|-------------|
| layer_arn | ARN of the Lambda layer version |
| layer_version | Version of the Lambda layer |
| layer_name | Name of the Lambda layer |

## Notes

- The layer must be packaged with a `python/` directory structure
- Dependencies should be installed to `python/` directory
- Layer size limit is 50 MB (zipped) or 250 MB (unzipped)
- Layers are versioned - each update creates a new version
