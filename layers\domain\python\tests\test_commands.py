"""Tests for command objects."""

from smartanalytics_domain.commands.get_or_create_agent_command import (
    GetOrCreateAgentCommand,
)
from smartanalytics_domain.commands.get_or_create_ring_group_command import (
    GetOrCreateRingGroupCommand,
)
from smartanalytics_domain.commands.get_or_create_tenant_command import (
    GetOrCreateTenantCommand,
)
from smartanalytics_domain.commands.process_agent_event_command import (
    ProcessAgentEventCommand,
)


class TestCommands:
    """Test command objects."""

    def test_get_or_create_tenant_command(self):
        """Test GetOrCreateTenantCommand creation."""
        cmd = GetOrCreateTenantCommand(tenant_name="TestTenant")

        assert cmd.tenant_name == "TestTenant"
        assert cmd.tenant_display_name is None

    def test_get_or_create_tenant_command_with_display_name(self):
        """Test GetOrCreateTenantCommand with display name."""
        cmd = GetOrCreateTenantCommand(
            tenant_name="TestTenant", tenant_display_name="Test Tenant Display"
        )

        assert cmd.tenant_name == "TestTenant"
        assert cmd.tenant_display_name == "Test Tenant Display"

    def test_get_or_create_agent_command(self):
        """Test GetOrCreateAgentCommand creation."""
        cmd = GetOrCreateAgentCommand(
            tenant_key=1, agent_name="TestAgent", agent_role="Operator"
        )

        assert cmd.tenant_key == 1
        assert cmd.agent_name == "TestAgent"
        assert cmd.agent_role == "Operator"

    def test_get_or_create_ring_group_command(self):
        """Test GetOrCreateRingGroupCommand creation."""
        cmd = GetOrCreateRingGroupCommand(
            tenant_key=1,
            ring_group_name="TestQueue",
            ring_group_uri="sip:<EMAIL>",
        )

        assert cmd.tenant_key == 1
        assert cmd.ring_group_name == "TestQueue"
        assert cmd.ring_group_uri == "sip:<EMAIL>"

    def test_process_agent_event_command(self):
        """Test ProcessAgentEventCommand creation."""
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
        <LogEvent>
            <timestamp>2025-01-01T12:00:00Z</timestamp>
            <agencyOrElement>TestTenant</agencyOrElement>
            <agent>TestAgent</agent>
            <eventType>ACDLogin</eventType>
        </LogEvent>"""

        cmd = ProcessAgentEventCommand(
            xml_content=xml_content, sqs_message_id="test-msg-123"
        )

        assert cmd.xml_content == xml_content
        assert cmd.sqs_message_id == "test-msg-123"
        assert cmd.client_timezone == "America/New_York"

    def test_process_agent_event_command_with_timezone(self):
        """Test ProcessAgentEventCommand with custom timezone."""
        xml_content = "<LogEvent></LogEvent>"

        cmd = ProcessAgentEventCommand(
            xml_content=xml_content,
            sqs_message_id="test-msg-123",
            client_timezone="America/Chicago",
        )

        assert cmd.xml_content == xml_content
        assert cmd.sqs_message_id == "test-msg-123"
        assert cmd.client_timezone == "America/Chicago"
