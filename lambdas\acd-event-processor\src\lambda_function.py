"""AWS Lambda function for processing ACD events from SQS.

This Lambda function processes ACD (Automatic Call Distribution) events
received from SQS queues, parses XML content, and stores events in Aurora PostgreSQL
using the Command/Handler pattern with shared domain and infrastructure layers.
"""

import asyncio
import json
from typing import Any

from aws_lambda_powertools import Logger, Tracer
from aws_lambda_powertools.logging import correlation_paths
from aws_lambda_powertools.utilities.typing import LambdaContext
from aws_lambda_typing.events import SQSEvent
from smartanalytics_domain.command_handlers import (
    ProcessAgentEventCommandHandler,
    ProcessAgentEventsBatchCommandHandler,
)
from smartanalytics_domain.commands import (
    ProcessAgentEventCommand,
    ProcessAgentEventsBatchCommand,
)
from smartanalytics_infrastructure.database.session import get_async_session
from smartanalytics_infrastructure.unit_of_work import SqlAlchemyUnitOfWork
from smartanalytics_utilities.config.settings import get_settings

# Initialize AWS Lambda Powertools
logger: Logger = Logger(service="acd-event-processor")
tracer: Tracer = Tracer(service="acd-event-processor")

# Get settings
settings = get_settings()


async def _process_events_async(
    xml_contents: list[str], sqs_message_ids: list[str]
) -> dict[str, Any]:
    """Process events asynchronously using command/handler pattern.

    Args:
        xml_contents: List of XML content strings
        sqs_message_ids: List of SQS message IDs

    Returns:
        Processing result dictionary
    """
    async with get_async_session() as session:
        uow = SqlAlchemyUnitOfWork(lambda: session)

        if len(xml_contents) == 1:
            # Single event processing
            handler = ProcessAgentEventCommandHandler(uow)
            command = ProcessAgentEventCommand(
                xml_content=xml_contents[0],
                sqs_message_id=sqs_message_ids[0],
                client_timezone=settings.client_timezone,
            )
            result = await handler.handle(command)

            return {
                "total_events": 1,
                "processed_events": 1
                if result.success and not result.is_duplicate
                else 0,
                "duplicate_events": 1 if result.is_duplicate else 0,
                "failed_events": 0 if result.success else 1,
                "processing_time_ms": result.processing_time_ms,
                "event_hashes": [result.event_hash] if result.success else [],
            }
        # Batch processing
        batch_handler = ProcessAgentEventsBatchCommandHandler(uow)
        batch_command = ProcessAgentEventsBatchCommand(
            xml_contents=xml_contents,
            sqs_message_ids=sqs_message_ids,
            client_timezone=settings.client_timezone,
        )
        batch_result = await batch_handler.handle(batch_command)

        return {
            "total_events": batch_result.total_events,
            "processed_events": batch_result.processed_events,
            "duplicate_events": batch_result.duplicate_events,
            "failed_events": batch_result.failed_events,
            "processing_time_ms": batch_result.processing_time_ms,
            "batch_id": batch_result.batch_id,
            "event_hashes": batch_result.event_hashes,
        }


@tracer.capture_lambda_handler
@logger.inject_lambda_context(correlation_id_path=correlation_paths.LAMBDA_FUNCTION_URL)
def lambda_handler(event: SQSEvent, context: LambdaContext) -> dict[str, Any]:
    """AWS Lambda handler for processing ACD events from SQS.

    Args:
        event: SQS event containing ACD XML messages
        context: Lambda runtime context

    Returns:
        Processing result with statistics

    Raises:
        Exception: If critical processing error occurs
    """
    logger.info(
        "ACD Event Processor Lambda started",
        records_count=len(event["Records"]),
        function_name=context.function_name,
        function_version=context.function_version,
        memory_limit=context.memory_limit_in_mb,
        remaining_time=context.get_remaining_time_in_millis(),
    )

    try:
        # Extract XML content and message IDs from SQS messages
        xml_contents = []
        sqs_message_ids = []

        for record in event["Records"]:
            body = record.get("body", "")
            message_id = record.get("messageId", "")

            # Handle SNS-wrapped messages
            if record.get("eventSource") == "aws:sns":
                try:
                    sns_message = json.loads(body)
                    xml_content = sns_message.get("Message", body)
                except json.JSONDecodeError:
                    xml_content = body
            else:
                xml_content = body

            xml_contents.append(xml_content)
            sqs_message_ids.append(message_id)

        logger.info(
            "Extracted XML content from SQS messages",
            xml_count=len(xml_contents),
        )

        # Process events using command/handler pattern
        # Lambda doesn't have a running event loop, so we use asyncio.run()
        result = asyncio.run(_process_events_async(xml_contents, sqs_message_ids))

        logger.info(
            "ACD Event Processor Lambda completed",
            total_events=result.get("total_events", 0),
            processed_events=result.get("processed_events", 0),
            duplicate_events=result.get("duplicate_events", 0),
            failed_events=result.get("failed_events", 0),
            processing_time_ms=result.get("processing_time_ms", 0),
        )

        return {
            "statusCode": 200,
            "body": json.dumps(result),
            "headers": {
                "Content-Type": "application/json",
            },
        }

    except Exception as e:
        logger.error(
            "ACD Event Processor Lambda failed",
            error=str(e),
            exc_info=True,
        )

        # Re-raise to trigger SQS retry mechanism
        raise
