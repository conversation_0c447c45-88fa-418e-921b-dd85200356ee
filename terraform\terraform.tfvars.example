# Example Terraform variables file

# AWS Region
region = "us-east-1"

# Environment (dev, qa, prod)
environment = "dev"

# Country (us, ca, au)
country = "us"

# Global tags applied to all resources
global_tags = {
  Environment = "dev"
  Project     = "smart-analytics"
  Owner       = "csa"
  ManagedBy   = "terraform"
}

# SNS topic for Slack error notifications
slack_errors_alarm_topic_name = "csa-critical-alarms"

# Project directory (usually set by CI/CD)
project_dir = "."

# Customer configuration
service_customer_config = {
  customer1 = {
    # S3 bucket for Lambda code (created in separate repository)
    s3_lambda_code_bucket_name = "lambda-code-bucket"

    # KMS key for encryption
    kms_key_name = "lambda-code-key"

    # ACD processor configuration
    acd = {
      # Enable ACD processing for this customer
      enable = true

      # Lambda configuration
      lambda_zip_file_name = "agent-event-processor.zip"

      # SQS queue names
      queue_name = "customer1-acd-queue"
      dlq_name   = "customer1-acd-dlq"

      # VPC configuration
      subnet_ids         = ["subnet-12345678", "subnet-87654321"]
      vpc_id             = "vpc-12345678"

      # Aurora PostgreSQL configuration
      database_secret_name         = "aurora/customer1"
      database_cluster_identifier  = "aurora-cluster"
      database_cluster_resource_id = "aurora-cluster-resource"
      database_database_name      = "customer1"
      database_db_user_name       = "solacom"

      # Lambda environment variables
      environment_variables = {
        DATABASE_DATABASE_NAME = "customer1"
        LOG_LEVEL        = "INFO"
        BATCH_SIZE       = "10"
      }
    }

    # Customer-specific tags
    tags = {
      Customer   = "customer1"
      CostCenter = "analytics"
    }
  }
}
