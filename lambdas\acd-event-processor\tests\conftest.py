"""Shared pytest fixtures for ACD Event Processor tests."""

from datetime import UTC, datetime
from unittest.mock import AsyncMock, MagicMock

import pytest


@pytest.fixture
def sample_event_xmls():
    """Sample XML events for all event types."""
    return {
        "Login": """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2024-01-15T10:30:00Z</timestamp>
    <agencyOrElement>TestTenant</agencyOrElement>
    <agent>TestAgent001</agent>
    <eventType>Login</eventType>
    <login>
        <agentRole>operator</agentRole>
        <workstation>WS001</workstation>
        <operatorId>OP123</operatorId>
    </login>
</LogEvent>""",
        "Logout": """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2024-01-15T11:30:00Z</timestamp>
    <agencyOrElement>TestTenant</agencyOrElement>
    <agent>TestAgent001</agent>
    <eventType>Logout</eventType>
    <logout>
        <workstation>WS001</workstation>
    </logout>
</LogEvent>""",
        "ACDLogin": """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2024-01-15T10:35:00Z</timestamp>
    <agencyOrElement>TestTenant</agencyOrElement>
    <agent>TestAgent001</agent>
    <eventType>ACDLogin</eventType>
    <acdLogin>
        <agentRole>operator</agentRole>
        <ringGroupName>Support</ringGroupName>
        <ringGroupUri>sip:<EMAIL></ringGroupUri>
    </acdLogin>
</LogEvent>""",
        "ACDLogout": """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2024-01-15T11:35:00Z</timestamp>
    <agencyOrElement>TestTenant</agencyOrElement>
    <agent>TestAgent001</agent>
    <eventType>ACDLogout</eventType>
    <acdLogout>
        <ringGroupName>Support</ringGroupName>
        <ringGroupUri>sip:<EMAIL></ringGroupUri>
    </acdLogout>
</LogEvent>""",
        "AgentAvailable": """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2024-01-15T10:40:00Z</timestamp>
    <agencyOrElement>TestTenant</agencyOrElement>
    <agent>TestAgent001</agent>
    <eventType>AgentAvailable</eventType>
    <agentAvailable>
        <ringGroupName>Support</ringGroupName>
        <ringGroupUri>sip:<EMAIL></ringGroupUri>
    </agentAvailable>
</LogEvent>""",
        "AgentBusiedOut": """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2024-01-15T10:45:00Z</timestamp>
    <agencyOrElement>TestTenant</agencyOrElement>
    <agent>TestAgent001</agent>
    <eventType>AgentBusiedOut</eventType>
    <busiedOut>
        <busiedOutAction>Break</busiedOutAction>
        <busiedOutDuration>900</busiedOutDuration>
        <reasonCode>LUNCH</reasonCode>
    </busiedOut>
</LogEvent>""",
    }


@pytest.fixture
def mock_uow():
    """Create a mock unit of work with all repositories."""
    uow = MagicMock()
    uow.__aenter__ = AsyncMock(return_value=uow)
    uow.__aexit__ = AsyncMock(return_value=None)
    uow.commit = AsyncMock()
    uow.rollback = AsyncMock()

    # Mock tenant repository
    uow.tenants = MagicMock()
    uow.tenants.get_by_name = AsyncMock(return_value=None)
    uow.tenants.create = AsyncMock(return_value=1)

    # Mock agent repository
    uow.agents = MagicMock()
    uow.agents.get_by_tenant_and_name = AsyncMock(return_value=None)
    uow.agents.create = AsyncMock(return_value=1)

    # Mock ring group repository
    uow.ring_groups = MagicMock()
    uow.ring_groups.get_by_tenant_and_name = AsyncMock(return_value=None)
    uow.ring_groups.create = AsyncMock(return_value=1)

    # Mock agent events repository
    uow.agent_events = MagicMock()
    uow.agent_events.save = AsyncMock(return_value=1)
    uow.agent_events.get_by_hash = AsyncMock(return_value=None)

    return uow


@pytest.fixture
def sample_timestamp():
    """Sample timestamp for testing."""
    return datetime(2024, 1, 15, 10, 30, 0, tzinfo=UTC)


@pytest.fixture
def sample_sqs_message_id():
    """Sample SQS message ID."""
    return "test-message-123-456-789"


@pytest.fixture
def sample_client_timezone():
    """Sample client timezone."""
    return "America/New_York"
