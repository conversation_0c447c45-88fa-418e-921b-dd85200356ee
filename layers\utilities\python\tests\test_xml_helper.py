"""Unit tests for XML helper utilities."""

import pytest
from smartanalytics_utilities.utils.xml_helper import AC<PERSON><PERSON><PERSON>MLParser, XMLParser


class TestACDEventXMLParser:
    """Test cases for ACDEventXMLParser."""

    def test_xml_to_json_login_event(self):
        """Test parsing of login event XML."""
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2024-01-15T10:30:00Z</timestamp>
    <agencyOrElement>TestTenant</agencyOrElement>
    <agent>TestAgent</agent>
    <eventType>Login</eventType>
    <login>
        <mediaLabel>_ML_TEST123@TestTenant</mediaLabel>
        <uri>tel:+1234567890</uri>
        <agentRole>Test Role</agentRole>
        <tenantGroup>TestTenant</tenantGroup>
        <operatorId>123</operatorId>
        <workstation>WS001</workstation>
        <deviceName>Headset</deviceName>
        <reason>normal</reason>
    </login>
</LogEvent>"""

        result = ACDEventXMLParser.xml_to_json(xml_content)

        assert result["timestamp"] == "2024-01-15T10:30:00Z"
        assert result["agencyOrElement"] == "TestTenant"
        assert result["agent"] == "TestAgent"
        assert result["eventType"] == "Login"
        assert result["login"]["mediaLabel"] == "_ML_TEST123@TestTenant"
        assert result["login"]["uri"] == "tel:+1234567890"
        assert result["login"]["operatorId"] == "123"

    def test_xml_to_json_acd_login_event(self):
        """Test parsing of ACD login event XML."""
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2024-01-15T10:30:00Z</timestamp>
    <agencyOrElement>TestTenant</agencyOrElement>
    <agent>TestAgent</agent>
    <eventType>ACDLogin</eventType>
    <acdLogin>
        <agentUri>tel:+1234567890</agentUri>
        <ringGroupName>Test Queue</ringGroupName>
        <ringGroupUri>tel:+1234567890</ringGroupUri>
        <mediaLabel>_ML_TEST123@TestTenant</mediaLabel>
    </acdLogin>
</LogEvent>"""

        result = ACDEventXMLParser.xml_to_json(xml_content)

        assert result["timestamp"] == "2024-01-15T10:30:00Z"
        assert result["agencyOrElement"] == "TestTenant"
        assert result["agent"] == "TestAgent"
        assert result["eventType"] == "ACDLogin"
        assert result["acdLogin"]["agentUri"] == "tel:+1234567890"
        assert result["acdLogin"]["ringGroupName"] == "Test Queue"

    def test_xml_to_json_logout_event(self):
        """Test parsing of logout event XML."""
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2024-01-15T10:30:00Z</timestamp>
    <agencyOrElement>TestTenant</agencyOrElement>
    <agent>TestAgent</agent>
    <eventType>Logout</eventType>
    <logout>
        <responseCode>16</responseCode>
        <reason>normal</reason>
    </logout>
</LogEvent>"""

        result = ACDEventXMLParser.xml_to_json(xml_content)

        assert result["timestamp"] == "2024-01-15T10:30:00Z"
        assert result["agencyOrElement"] == "TestTenant"
        assert result["agent"] == "TestAgent"
        assert result["eventType"] == "Logout"
        assert result["logout"]["responseCode"] == "16"
        assert result["logout"]["reason"] == "normal"

    def test_xml_to_json_complex_nested_structure(self):
        """Test parsing of complex nested XML structure."""
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2024-01-15T10:30:00Z</timestamp>
    <agencyOrElement>TestTenant</agencyOrElement>
    <agent>TestAgent</agent>
    <eventType>ComplexEvent</eventType>
    <complexData>
        <level1>
            <level2>
                <value>test</value>
                <number>123</number>
            </level2>
            <array>
                <item>item1</item>
                <item>item2</item>
            </array>
        </level1>
    </complexData>
</LogEvent>"""

        result = ACDEventXMLParser.xml_to_json(xml_content)

        assert result["complexData"]["level1"]["level2"]["value"] == "test"
        assert result["complexData"]["level1"]["level2"]["number"] == "123"
        # xmltodict converts repeated elements to lists
        assert isinstance(result["complexData"]["level1"]["array"]["item"], list)
        assert result["complexData"]["level1"]["array"]["item"] == ["item1", "item2"]

    def test_xml_to_json_with_attributes(self):
        """Test parsing of XML with attributes."""
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent version="1.0" source="test">
    <timestamp>2024-01-15T10:30:00Z</timestamp>
    <agencyOrElement>TestTenant</agencyOrElement>
    <agent id="123">TestAgent</agent>
    <eventType>Login</eventType>
    <login status="active">
        <mediaLabel>_ML_TEST123@TestTenant</mediaLabel>
    </login>
</LogEvent>"""

        result = ACDEventXMLParser.xml_to_json(xml_content)

        # xmltodict preserves attributes with @ prefix
        assert result["@version"] == "1.0"
        assert result["@source"] == "test"
        assert result["agent"]["@id"] == "123"
        assert result["agent"]["#text"] == "TestAgent"
        assert result["login"]["@status"] == "active"

    def test_xml_to_json_invalid_xml(self):
        """Test handling of invalid XML."""
        invalid_xml = "This is not valid XML"

        with pytest.raises(Exception):
            ACDEventXMLParser.xml_to_json(invalid_xml)

    def test_xml_to_json_malformed_xml(self):
        """Test handling of malformed XML."""
        malformed_xml = """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2024-01-15T10:30:00Z</timestamp>
    <unclosed_tag>
</LogEvent>"""

        with pytest.raises(Exception):
            ACDEventXMLParser.xml_to_json(malformed_xml)

    def test_xml_to_json_empty_xml(self):
        """Test handling of empty XML."""
        empty_xml = ""

        with pytest.raises(Exception):
            ACDEventXMLParser.xml_to_json(empty_xml)

    def test_xml_to_json_xml_with_cdata(self):
        """Test parsing of XML with CDATA sections."""
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2024-01-15T10:30:00Z</timestamp>
    <agencyOrElement>TestTenant</agencyOrElement>
    <agent>TestAgent</agent>
    <eventType>Login</eventType>
    <description><![CDATA[This is a description with <special> characters & symbols]]></description>
</LogEvent>"""

        result = ACDEventXMLParser.xml_to_json(xml_content)

        assert (
            result["description"]
            == "This is a description with <special> characters & symbols"
        )

    def test_xml_to_json_xml_with_namespaces(self):
        """Test parsing of XML with namespaces using base XMLParser."""
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<ns:LogEvent xmlns:ns="http://example.com/namespace">
    <ns:timestamp>2024-01-15T10:30:00Z</ns:timestamp>
    <ns:agencyOrElement>TestTenant</ns:agencyOrElement>
    <ns:agent>TestAgent</ns:agent>
    <ns:eventType>Login</ns:eventType>
</ns:LogEvent>"""

        # Use base XMLParser to avoid validation
        result = XMLParser.xml_to_json(xml_content)

        # xmltodict handles namespaces by including them in the key names
        assert result is not None
        assert "ns:LogEvent" in result
        log_event = result["ns:LogEvent"]
        assert log_event["ns:timestamp"] == "2024-01-15T10:30:00Z"
        assert log_event["ns:agencyOrElement"] == "TestTenant"

    def test_xml_to_json_logging(self):
        """Test that XML parsing works with logging."""
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2024-01-15T10:30:00Z</timestamp>
    <agencyOrElement>TestTenant</agencyOrElement>
    <agent>TestAgent</agent>
    <eventType>Login</eventType>
</LogEvent>"""

        result = ACDEventXMLParser.xml_to_json(xml_content)

        # Verify parsing was successful
        assert result is not None
        assert result["timestamp"] == "2024-01-15T10:30:00Z"

    def test_xml_to_json_special_characters(self):
        """Test parsing of XML with special characters."""
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2024-01-15T10:30:00Z</timestamp>
    <agencyOrElement>Test&amp;Tenant</agencyOrElement>
    <agent>Test&lt;Agent&gt;</agent>
    <eventType>Login</eventType>
    <description>Special chars: &quot;quotes&quot; &amp; &apos;apostrophes&apos;</description>
</LogEvent>"""

        result = ACDEventXMLParser.xml_to_json(xml_content)

        assert result["agencyOrElement"] == "Test&Tenant"
        assert result["agent"] == "Test<Agent>"
        assert result["description"] == "Special chars: \"quotes\" & 'apostrophes'"

    def test_xml_to_json_unicode_characters(self):
        """Test parsing of XML with Unicode characters."""
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2024-01-15T10:30:00Z</timestamp>
    <agencyOrElement>Tëst Tënant</agencyOrElement>
    <agent>Tëst Agënt</agent>
    <eventType>Login</eventType>
    <description>Unicode: 你好 🌟 café</description>
</LogEvent>"""

        result = ACDEventXMLParser.xml_to_json(xml_content)

        assert result["agencyOrElement"] == "Tëst Tënant"
        assert result["agent"] == "Tëst Agënt"
        assert result["description"] == "Unicode: 你好 🌟 café"
