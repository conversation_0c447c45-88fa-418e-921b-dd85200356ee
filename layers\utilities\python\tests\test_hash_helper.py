"""Comprehensive tests for hash helper utilities.

This module consolidates all hash-related tests to avoid duplication.
Tests cover hash generation, determinism, consistency, and edge cases.
"""

import hashlib
import json

from smartanalytics_utilities.utils.hash_helper import generate_event_hash, hash_event


class TestHashGeneration:
    """Test hash generation functionality."""

    def test_generate_event_hash_basic(self):
        """Test basic event hash generation."""
        event_data = {
            "timestamp": "2024-01-15T10:30:00Z",
            "agent_name": "TestAgent",
            "tenant_name": "TestTenant",
            "event_type": "Login",
        }

        hash_value = generate_event_hash(event_data)

        assert isinstance(hash_value, str)
        assert len(hash_value) == 64
        # Should be valid hex
        int(hash_value, 16)

    def test_hash_event_function(self):
        """Test hash_event function (alias for generate_event_hash)."""
        data = {"test": "data"}
        event_hash = hash_event(data)

        assert isinstance(event_hash, str)
        assert len(event_hash) == 64
        assert all(c in "0123456789abcdef" for c in event_hash)

    def test_generate_event_hash_empty_dict(self):
        """Test hash generation with empty dictionary."""
        event_data = {}

        hash_value = generate_event_hash(event_data)

        assert isinstance(hash_value, str)
        assert len(hash_value) == 64

    def test_generate_event_hash_with_none_values(self):
        """Test hash generation with None values."""
        event_data = {
            "timestamp": "2024-01-15T10:30:00Z",
            "agent_name": "TestAgent",
            "tenant_name": "TestTenant",
            "event_type": "Login",
            "optional_field": None,
        }

        hash_value = generate_event_hash(event_data)

        assert isinstance(hash_value, str)
        assert len(hash_value) == 64


class TestHashDeterminism:
    """Test hash determinism and consistency."""

    def test_generate_event_hash_deterministic(self):
        """Test that hash generation is deterministic."""
        event_data = {
            "timestamp": "2024-01-15T10:30:00Z",
            "agent_name": "TestAgent",
            "tenant_name": "TestTenant",
            "event_type": "Login",
        }

        hash1 = generate_event_hash(event_data)
        hash2 = generate_event_hash(event_data)

        assert hash1 == hash2

    def test_generate_event_hash_consistency(self):
        """Test that hash generation is consistent across multiple runs."""
        event_data = {
            "timestamp": "2024-01-15T10:30:00Z",
            "agent_name": "TestAgent",
            "tenant_name": "TestTenant",
            "event_type": "Login",
        }

        hashes = [generate_event_hash(event_data) for _ in range(5)]

        # All hashes should be identical
        assert all(h == hashes[0] for h in hashes)

    def test_generate_event_hash_different_order(self):
        """Test that hash is consistent regardless of dict key order."""
        data1 = {"b": 2, "a": 1, "c": 3}
        data2 = {"a": 1, "b": 2, "c": 3}

        hash1 = generate_event_hash(data1)
        hash2 = generate_event_hash(data2)

        assert hash1 == hash2

    def test_generate_event_hash_manual_verification(self):
        """Test hash generation against manual calculation."""
        event_data = {"test": "data"}

        # Generate hash using our function
        hash_value = generate_event_hash(event_data)

        # Manual calculation for comparison
        normalized_data = json.dumps(event_data, sort_keys=True, separators=(",", ":"))
        expected_hash = hashlib.sha256(normalized_data.encode("utf-8")).hexdigest()

        assert hash_value == expected_hash


class TestHashUniqueness:
    """Test hash uniqueness for different data."""

    def test_generate_event_hash_different_data(self):
        """Test that different data produces different hashes."""
        event_data1 = {
            "timestamp": "2024-01-15T10:30:00Z",
            "agent_name": "TestAgent1",
            "tenant_name": "TestTenant",
            "event_type": "Login",
        }

        event_data2 = {
            "timestamp": "2024-01-15T10:30:00Z",
            "agent_name": "TestAgent2",
            "tenant_name": "TestTenant",
            "event_type": "Login",
        }

        hash1 = generate_event_hash(event_data1)
        hash2 = generate_event_hash(event_data2)

        assert hash1 != hash2

    def test_different_data_different_hash(self):
        """Test that different tenant/agent produces different hashes."""
        data1 = {"tenant": "tenant1", "agent": "agent1"}
        data2 = {"tenant": "tenant2", "agent": "agent2"}

        hash1 = generate_event_hash(data1)
        hash2 = generate_event_hash(data2)

        assert hash1 != hash2


class TestHashWithNestedData:
    """Test hash generation with complex nested structures."""

    def test_generate_event_hash_with_nested_data(self):
        """Test hash generation with nested data."""
        event_data = {
            "timestamp": "2024-01-15T10:30:00Z",
            "agent_name": "TestAgent",
            "tenant_name": "TestTenant",
            "event_type": "Login",
            "event_data": {
                "login": {
                    "mediaLabel": "_ML_TEST123@TestTenant",
                    "uri": "tel:+1234567890",
                }
            },
        }

        hash_value = generate_event_hash(event_data)

        assert isinstance(hash_value, str)
        assert len(hash_value) == 64

    def test_generate_event_hash_nested_dict(self):
        """Test hash generation with nested dictionaries."""
        data = {
            "tenant": "test",
            "event": {
                "type": "LOGIN",
                "details": {"ip": "***********", "user_agent": "test-agent"},
            },
        }

        event_hash = generate_event_hash(data)
        assert len(event_hash) == 64

    def test_hash_event_with_nested_data(self):
        """Test hash_event with nested data."""
        data = {
            "tenant": "test",
            "event": {"type": "LOGIN", "timestamp": "2024-01-01T12:00:00Z"},
        }
        event_hash = hash_event(data)
        assert len(event_hash) == 64
