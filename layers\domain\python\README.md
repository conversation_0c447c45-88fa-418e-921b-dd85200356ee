# SmartAnalytics Domain Layer

This package contains the domain models, value objects, and business logic for the SmartAnalytics system.

## Features

- Domain models for agent events
- Value objects for type safety
- Business logic and validation
- Repository interfaces (ports)
- Domain services

## Installation

```bash
pip install -e .
```

## Usage

```python
from smartanalytics_domain.models.agent_event import Agent<PERSON>vent, EventType
from smartanalytics_domain.models.value_objects import TenantId, AgentId
from smartanalytics_domain.services.agent_event_service import AgentEventService
```
