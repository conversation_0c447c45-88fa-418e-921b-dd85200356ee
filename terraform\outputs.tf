# Lambda function outputs for each customer
output "acd_processor_functions" {
  description = "Map of customer ACD processor Lambda functions"
  value = {
    for customer, config in module.acd_processor : customer => {
      function_name = config.lambda_function_name
      function_arn  = config.lambda_function_arn
      role_arn      = config.lambda_role_arn
      log_group_arn = config.cloudwatch_log_group_arn
    }
  }
}

# CloudWatch alarm outputs for monitoring
output "acd_processor_alarms" {
  description = "Map of customer ACD processor CloudWatch alarms"
  value = {
    for customer, config in module.acd_processor : customer => {
      errors_alarm_arn      = config.lambda_errors_alarm_arn
      duration_alarm_arn    = config.lambda_duration_alarm_arn
      invocations_alarm_arn = config.lambda_invocations_alarm_arn
    }
  }
}
