[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "smartanalytics-utilities"
version = "0.1.0"
description = "SmartAnalytics utilities layer - configuration, logging, and utility functions"
authors = [
    {name = "Smart Analytics Team"}
]
readme = "README.md"
requires-python = ">=3.12"
classifiers = [
    "Intended Audience :: Developers",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    # Core dependencies
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",

    # AWS dependencies
    "boto3>=1.34.0",
    "botocore>=1.34.0",

    # Observability
    "aws-lambda-powertools[all]>=2.30.0",

    # Utilities
    "python-dateutil>=2.8.2",
    "pytz>=2023.3",
    "lxml>=4.9.3",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",

    # Code quality
    "black>=23.12.0",
    "ruff>=0.1.9",
    "mypy>=1.8.0",
    "bandit>=1.7.5",

    # Type stubs
    "types-python-dateutil>=2.8.19",
    "types-pytz>=2023.3.1.1",
    "boto3-stubs[essential]>=1.34.0",
]
