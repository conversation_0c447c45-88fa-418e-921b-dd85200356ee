"""SQLAlchemy Unit of Work implementation."""

import types
from collections.abc import Callable
from typing import TYPE_CHECKING

from smartanalytics_domain.ports.unit_of_work import UnitOfWork
from smartanalytics_infrastructure.repositories.agent_event_repository import (
    SqlAlchemyAgentEventRepository,
)
from smartanalytics_infrastructure.repositories.agent_repository import (
    SqlAlchemyAgentRepository,
)
from smartanalytics_infrastructure.repositories.ring_group_repository import (
    SqlAlchemyRingGroupRepository,
)
from smartanalytics_infrastructure.repositories.tenant_repository import (
    SqlAlchemyTenantRepository,
)

if TYPE_CHECKING:
    from sqlalchemy.ext.asyncio import AsyncSession


class SqlAlchemyUnitOfWork(UnitOfWork):
    """SQLAlchemy implementation of Unit of Work pattern."""

    def __init__(self, session_factory: Callable[[], "AsyncSession"]) -> None:
        """Initialize with session factory."""
        self.session_factory = session_factory
        self.session: AsyncSession | None = None

    async def __aenter__(self) -> "SqlAlchemyUnitOfWork":
        """Enter the async context manager."""
        self.session = self.session_factory()
        if self.session is None:
            raise RuntimeError("Session factory must return a valid session")

        # Initialize repositories
        self.agent_events = SqlAlchemyAgentEventRepository(self.session)
        self.agents = SqlAlchemyAgentRepository(self.session)
        self.tenants = SqlAlchemyTenantRepository(self.session)
        self.ring_groups = SqlAlchemyRingGroupRepository(self.session)

        return self

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: types.TracebackType | None,
    ) -> None:
        """Exit the async context manager."""
        if exc_type is not None:
            await self.rollback()
        if self.session is not None:
            await self.session.close()

    async def commit(self) -> None:
        """Commit the current transaction."""
        if self.session is not None:
            await self.session.commit()

    async def rollback(self) -> None:
        """Rollback the current transaction."""
        if self.session is not None:
            await self.session.rollback()
