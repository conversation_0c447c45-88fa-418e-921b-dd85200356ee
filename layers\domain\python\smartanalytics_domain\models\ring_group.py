"""Ring group domain entity."""

from dataclasses import dataclass
from datetime import datetime


@dataclass(frozen=True)
class RingGroup:
    """Ring group domain entity representing a call routing group.

    This is an immutable value object representing a ring group in the system.
    """

    ring_group_key: int
    tenant_key: int
    ring_group_name: str
    created_at: datetime

    def __post_init__(self) -> None:
        """Validate ring group data."""
        if not self.ring_group_name:
            raise ValueError("Ring group name cannot be empty")
        if self.tenant_key <= 0:
            raise ValueError("Tenant key must be positive")
