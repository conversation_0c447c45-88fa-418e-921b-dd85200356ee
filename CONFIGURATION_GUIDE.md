# Smart Analytics Processors - Configuration Guide

## Overview

This repository uses a **centralized configuration approach** that supports multiple programming languages while maintaining consistency across all Lambda functions.

## Configuration Structure

### Top-Level Configuration Files

```
smartanalytics-processors/
├── pyproject.toml              # Python tool configuration (shared)
├── ruff.toml                   # Ruff linting/formatting rules (shared)
├── .pre-commit-config.yaml     # Pre-commit hooks (all languages)
├── requirements-ci.txt         # CI/CD dependencies
└── .gitlab-ci.yml              # CI/CD pipeline
```

### Language-Specific Lambda Structure

```
lambdas/
├── acd-event-processor/     # Python Lambda
│   ├── pyproject.toml         # Project metadata only
│   ├── requirements.txt       # Runtime dependencies
│   ├── requirements-dev.txt   # Development dependencies
│   └── src/                   # Source code
├── future-go-lambda/          # Go Lambda (example)
│   ├── go.mod                 # Go module definition
│   ├── go.sum                 # Go dependencies
│   ├── .golangci.yml          # Go linting configuration
│   └── main.go                # Source code
```

## Python Configuration Details

### Tool Consolidation ✅

- **Ruff**: Handles both linting AND formatting (replaces Black + flake8)
- **mypy**: Type checking
- **bandit**: Security scanning
- **pytest**: Testing framework

### Configuration Inheritance

1. **Top-level `pyproject.toml`**: Shared tool configurations
2. **Lambda `pyproject.toml`**: Project metadata + overrides (if needed)
3. **Top-level `ruff.toml`**: Comprehensive linting rules
4. **Top-level `.pre-commit-config.yaml`**: Consistent pre-commit hooks

## Multi-Language Support

### Current: Python ✅

- Centralized Python tool configuration
- Consistent formatting and linting
- Shared CI/CD pipeline

## Benefits of This Structure

### ✅ Consistency
- All Python lambdas use identical tool configurations
- Single source of truth for code quality rules
- Uniform CI/CD pipeline behavior

### ✅ Maintainability
- Tool updates happen in one place
- Easy to add new lambdas
- Clear separation of concerns

### ✅ Extensibility
- Language-specific configurations in their own directories
- CI/CD pipeline easily extended for new languages
- Pre-commit hooks can be language-aware

### ✅ Developer Experience
- Single pre-commit setup for entire repository
- Consistent commands across all lambdas
- Clear documentation and examples

## Usage Examples

### For Python Lambdas

```bash
# Format and lint (uses top-level ruff.toml)
ruff format .
ruff check . --fix

# Type checking (uses top-level pyproject.toml)
mypy src/

# Testing (uses top-level pyproject.toml)
pytest

# Pre-commit (uses top-level .pre-commit-config.yaml)
pre-commit run --all-files
```

### Adding a New Python Lambda

1. Create lambda directory: `lambdas/new-lambda/`
2. Add minimal `pyproject.toml` with project metadata
3. Add `requirements.txt` and `requirements-dev.txt`
4. Update `.gitlab-ci.yml` LAMBDA_FUNCTIONS variable
5. All tool configurations are automatically inherited!
