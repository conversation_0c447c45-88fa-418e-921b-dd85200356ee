# Lambda Layer Version Resource
resource "aws_lambda_layer_version" "layer" {
  layer_name               = var.layer_name
  description              = var.description
  compatible_runtimes      = var.compatible_runtimes
  compatible_architectures = var.compatible_architectures

  s3_bucket = var.s3_bucket
  s3_key    = var.s3_key

  source_code_hash = var.source_code_hash

  lifecycle {
    create_before_destroy = true
  }

  skip_destroy = var.skip_destroy
}
