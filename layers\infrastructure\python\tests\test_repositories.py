"""Tests for repository implementations."""

from datetime import UTC, datetime
from unittest.mock import Async<PERSON>ock, MagicMock, <PERSON><PERSON>

import pytest
from smartanalytics_domain.models.agent_event import Agent<PERSON>vent, EventType
from smartanalytics_infrastructure.database.models import (
    DimAgent,
    DimRingGroup,
    DimTenant,
)
from smartanalytics_infrastructure.repositories.agent_event_repository import (
    SqlAlchemyAgentEventRepository,
)
from smartanalytics_infrastructure.repositories.agent_repository import (
    SqlAlchemyAgentRepository,
)
from smartanalytics_infrastructure.repositories.ring_group_repository import (
    SqlAlchemyRingGroupRepository,
)
from smartanalytics_infrastructure.repositories.tenant_repository import (
    SqlAlchemyTenantRepository,
)
from sqlalchemy.ext.asyncio import AsyncSession


class TestTenantRepository:
    """Test TenantRepository implementation."""

    @pytest.fixture
    def mock_session(self):
        """Mock async database session."""
        session = Mock(spec=AsyncSession)
        session.execute = AsyncMock()
        session.add = Mock()
        session.commit = AsyncMock()
        session.flush = AsyncMock()
        return session

    @pytest.fixture
    def repository(self, mock_session):
        """Create TenantRepository instance with mock session."""
        return SqlAlchemyTenantRepository(mock_session)

    @pytest.mark.asyncio
    async def test_get_by_name_found(self, repository, mock_session):
        """Test getting tenant by name when found."""
        # Setup mock result
        mock_tenant = DimTenant(
            tenant_key=1, tenant_name="TestTenant", timezone_name="America/Chicago"
        )

        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_tenant
        mock_session.execute.return_value = mock_result

        # Test repository method
        result = await repository.get_by_name("TestTenant")

        # Verify result is a domain object
        assert result is not None
        assert result.tenant_name == "TestTenant"
        assert result.timezone_name == "America/Chicago"
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_by_name_not_found(self, repository, mock_session):
        """Test getting tenant by name when not found."""
        # Setup mock to return None
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Test repository method
        result = await repository.get_by_name("NonExistentTenant")

        # Verify None is returned
        assert result is None
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_tenant(self, repository, mock_session):
        """Test creating a new tenant."""
        tenant_name = "NewTenant"
        timezone_name = "America/Chicago"

        # Mock the flush to set tenant_key
        def mock_flush():
            # Simulate database setting the primary key
            mock_session.add.call_args[0][0].tenant_key = 123

        mock_session.flush.side_effect = mock_flush

        # Test repository method
        result = await repository.create(tenant_name, timezone_name)

        # Verify tenant creation returns tenant_key
        assert result == 123

        # Verify database interactions
        mock_session.add.assert_called_once()
        mock_session.flush.assert_called_once()


class TestAgentRepository:
    """Test AgentRepository implementation."""

    @pytest.fixture
    def mock_session(self):
        """Mock async database session."""
        session = Mock(spec=AsyncSession)
        session.execute = AsyncMock()
        session.add = Mock()
        session.commit = AsyncMock()
        session.flush = AsyncMock()
        return session

    @pytest.fixture
    def repository(self, mock_session):
        """Create AgentRepository instance with mock session."""
        return SqlAlchemyAgentRepository(mock_session)

    @pytest.mark.asyncio
    async def test_get_by_tenant_and_name_found(self, repository, mock_session):
        """Test getting agent by tenant and name when found."""
        # Setup mock result
        mock_agent = DimAgent(
            agent_key=1,
            tenant_key=1,
            agent_name="TestAgent001",
            agent_role="Supervisor",
        )

        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_agent
        mock_session.execute.return_value = mock_result

        # Test repository method
        result = await repository.get_by_tenant_and_name(1, "TestAgent001")

        # Verify result is a domain object
        assert result is not None
        assert result.agent_name == "TestAgent001"
        assert result.tenant_key == 1
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_agent(self, repository, mock_session):
        """Test creating a new agent."""
        tenant_key = 1
        agent_name = "NewAgent"
        agent_role = "Agent"

        # Mock the flush to set agent_key
        def mock_flush():
            # Simulate database setting the primary key
            mock_session.add.call_args[0][0].agent_key = 456

        mock_session.flush.side_effect = mock_flush

        # Test repository method
        result = await repository.create(tenant_key, agent_name, agent_role)

        # Verify agent creation returns agent_key
        assert result == 456

        # Verify database interactions
        mock_session.add.assert_called_once()
        mock_session.flush.assert_called_once()


class TestRingGroupRepository:
    """Test RingGroupRepository implementation."""

    @pytest.fixture
    def mock_session(self):
        """Mock async database session."""
        session = Mock(spec=AsyncSession)
        session.execute = AsyncMock()
        session.add = Mock()
        session.commit = AsyncMock()
        session.flush = AsyncMock()
        return session

    @pytest.fixture
    def repository(self, mock_session):
        """Create RingGroupRepository instance with mock session."""
        return SqlAlchemyRingGroupRepository(mock_session)

    @pytest.mark.asyncio
    async def test_get_by_tenant_and_name_found(self, repository, mock_session):
        """Test getting ring group by tenant and name when found."""
        # Setup mock result
        mock_ring_group = DimRingGroup(
            ring_group_key=1,
            tenant_key=1,
            ring_group_name="Support",
            ring_group_uri="sip:<EMAIL>",
        )
        # Set created_at_utc for the repository logic
        mock_ring_group.created_at_utc = datetime.now(UTC)

        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_ring_group
        mock_session.execute.return_value = mock_result

        # Test repository method
        result = await repository.get_by_tenant_and_name(1, "Support")

        # Verify result is a domain object
        assert result is not None
        assert result.ring_group_name == "Support"
        assert result.tenant_key == 1
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_ring_group(self, repository, mock_session):
        """Test creating a new ring group."""
        tenant_key = 1
        ring_group_name = "Sales"

        # Mock the flush to set ring_group_key
        def mock_flush():
            # Simulate database setting the primary key
            mock_session.add.call_args[0][0].ring_group_key = 789

        mock_session.flush.side_effect = mock_flush

        # Test repository method
        result = await repository.create(tenant_key, ring_group_name)

        # Verify ring group creation returns ring_group_key
        assert result == 789

        # Verify database interactions
        mock_session.add.assert_called_once()
        mock_session.flush.assert_called_once()


class TestAgentEventRepository:
    """Test AgentEventRepository implementation."""

    @pytest.fixture
    def mock_session(self):
        """Mock async database session."""
        session = Mock(spec=AsyncSession)
        session.execute = AsyncMock()
        session.add = Mock()
        session.commit = AsyncMock()
        session.flush = AsyncMock()
        return session

    @pytest.fixture
    def repository(self, mock_session):
        """Create AgentEventRepository instance with mock session."""
        return SqlAlchemyAgentEventRepository(mock_session)

    @pytest.mark.asyncio
    async def test_save_event_without_ring_group(self, repository, mock_session):
        """Test saving a single event without ring group."""
        # Create a mock agent event
        agent_event = Mock(spec=AgentEvent)
        agent_event.event_type = EventType.LOGIN
        agent_event.event_hash = "test_hash_456"
        agent_event.timestamp_utc = datetime.now(UTC)
        agent_event.timestamp_local = datetime.now(UTC)
        agent_event.operator_id = "OP123"
        agent_event.workstation = "WS001"
        agent_event.media_label = None
        agent_event.uri = None
        agent_event.device_name = None
        agent_event.busied_out_action = None
        agent_event.busied_out_duration = None
        agent_event.reason_code = None
        agent_event.json_data = {"workstation": "WS001"}

        dimension_keys = {"tenant_key": 1, "agent_key": 1, "ring_group_key": None}

        # Mock the flush to set event_key
        def mock_flush():
            # Simulate database setting the primary key
            mock_session.add.call_args[0][0].event_key = 999

        mock_session.flush.side_effect = mock_flush

        # Test repository method
        result = await repository.save(agent_event, dimension_keys)

        # Verify event creation returns event_key
        assert result == 999

        # Verify database interactions
        mock_session.add.assert_called_once()
        mock_session.flush.assert_called_once()

    @pytest.mark.asyncio
    async def test_save_batch_events(self, repository, mock_session):
        """Test saving multiple events in batch."""

        # Create mock agent events
        agent_events = []
        dimension_keys_list = []

        for i in range(2):
            event = Mock(spec=AgentEvent)
            event.event_type = EventType.LOGIN
            event.event_hash = f"hash_{i}"
            event.timestamp_utc = datetime.now(UTC)
            event.timestamp_local = datetime.now(UTC)
            event.operator_id = f"OP{i}"
            event.workstation = "WS001"
            event.media_label = None
            event.uri = None
            event.device_name = None
            event.busied_out_action = None
            event.busied_out_duration = None
            event.reason_code = None
            event.json_data = {"workstation": "WS001"}

            agent_events.append(event)
            dimension_keys_list.append(
                {"tenant_key": 1, "agent_key": 1, "ring_group_key": None}
            )

        # Mock the execute result for batch insert
        mock_result = MagicMock()
        mock_result.fetchall.return_value = [(100,), (101,)]
        mock_session.execute.return_value = mock_result

        # Test repository method
        results = await repository.save_batch(agent_events, dimension_keys_list)

        # Verify batch creation returns event_keys
        assert results == [100, 101]

        # Verify database interactions
        mock_session.execute.assert_called_once()
