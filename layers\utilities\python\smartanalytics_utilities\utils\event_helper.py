"""Helper functions for event data extraction and transformation."""

from typing import Any

# Event type to nested XML element mapping
EVENT_TYPE_TO_NESTED_ELEMENT: dict[str, str] = {
    "ACDLogin": "acdLogin",
    "ACDLogout": "acdLogout",
    "Login": "login",
    "Logout": "logout",
    "AgentAvailable": "agentAvailable",
    "AgentBusiedOut": "busiedOut",
}


def extract_nested_event_data(
    json_data: dict[str, Any], event_type: str
) -> dict[str, Any]:
    """Extract data from nested event-specific XML elements.

    Different event types have different nested structures in the XML:
    - ACDLogin -> acdLogin
    - ACDLogout -> acdLogout
    - Login -> login
    - Logout -> logout
    - AgentAvailable -> agentAvailable
    - AgentBusiedOut -> busiedOut

    Args:
        json_data: Parsed JSON data from XML
        event_type: Event type string

    Returns:
        Dictionary with extracted nested data

    Example:
        >>> json_data = {"eventType": "ACDLogin", "acdLogin": {"agentRole": "operator"}}
        >>> extract_nested_event_data(json_data, "ACDLogin")
        {"agentRole": "operator"}
    """
    nested_data: dict[str, Any] = {}

    nested_key = EVENT_TYPE_TO_NESTED_ELEMENT.get(event_type)
    if nested_key and nested_key in json_data:
        nested_element = json_data[nested_key]
        if isinstance(nested_element, dict):
            nested_data = nested_element

    return nested_data
