"""Unit tests for ProcessAgentEventCommandHandler."""

from unittest.mock import AsyncMock, MagicMock

import pytest
from smartanalytics_domain.command_handlers import <PERSON><PERSON>gent<PERSON>vent<PERSON>ommandHandler
from smartanalytics_domain.commands import ProcessAgentEventCommand
from smartanalytics_domain.exceptions.database_exceptions import DuplicateRecordError


@pytest.fixture
def mock_uow():
    """Create a mock unit of work."""
    uow = MagicMock()
    uow.__aenter__ = AsyncMock(return_value=uow)
    uow.__aexit__ = AsyncMock(return_value=None)
    uow.commit = AsyncMock()
    uow.rollback = AsyncMock()

    # Mock repositories
    uow.tenants = MagicMock()
    uow.tenants.get_by_name = AsyncMock(return_value=None)
    uow.tenants.create = AsyncMock(return_value=1)

    uow.agents = MagicMock()
    uow.agents.get_by_tenant_and_name = AsyncMock(return_value=None)
    uow.agents.create = AsyncMock(return_value=1)

    uow.ring_groups = MagicMock()
    uow.ring_groups.get_by_tenant_and_name = AsyncMock(return_value=None)
    uow.ring_groups.create = AsyncMock(return_value=1)

    uow.agent_events = MagicMock()
    uow.agent_events.save = AsyncMock(return_value=1)

    return uow


@pytest.fixture
def sample_login_xml():
    """Sample Login event XML."""
    return """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2024-01-15T10:30:00Z</timestamp>
    <agencyOrElement>TestTenant</agencyOrElement>
    <agent>TestAgent001</agent>
    <eventType>Login</eventType>
    <login>
        <agentRole>operator</agentRole>
        <workstation>WS001</workstation>
        <operatorId>OP123</operatorId>
    </login>
</LogEvent>"""


@pytest.fixture
def sample_acd_login_xml():
    """Sample ACDLogin event XML."""
    return """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <timestamp>2024-01-15T10:30:00Z</timestamp>
    <agencyOrElement>TestTenant</agencyOrElement>
    <agent>TestAgent001</agent>
    <eventType>ACDLogin</eventType>
    <acdLogin>
        <agentRole>operator</agentRole>
        <ringGroupName>Support</ringGroupName>
        <ringGroupUri>sip:<EMAIL></ringGroupUri>
    </acdLogin>
</LogEvent>"""


@pytest.mark.asyncio
async def test_process_agent_event_success(mock_uow, sample_login_xml):
    """Test successful processing of agent event."""
    # Arrange
    handler = ProcessAgentEventCommandHandler(mock_uow)
    command = ProcessAgentEventCommand(
        xml_content=sample_login_xml,
        sqs_message_id="test-message-123",
        client_timezone="America/New_York",
    )

    # Act
    result = await handler.handle(command)

    # Assert
    assert result.success is True
    assert result.is_duplicate is False
    assert result.event_hash is not None
    assert len(result.event_hash) == 64  # SHA-256 hash
    assert result.event_type == "Login"

    # Verify tenant was created
    mock_uow.tenants.get_by_name.assert_called_once_with("TestTenant")
    mock_uow.tenants.create.assert_called_once()

    # Verify agent was created
    mock_uow.agents.get_by_tenant_and_name.assert_called_once()
    mock_uow.agents.create.assert_called_once()

    # Verify event was saved
    mock_uow.agent_events.save.assert_called_once()

    # Verify transaction was committed
    mock_uow.commit.assert_called_once()


@pytest.mark.asyncio
async def test_process_agent_event_duplicate(mock_uow, sample_login_xml):
    """Test handling of duplicate event."""
    # Arrange
    handler = ProcessAgentEventCommandHandler(mock_uow)
    command = ProcessAgentEventCommand(
        xml_content=sample_login_xml,
        sqs_message_id="test-message-123",
        client_timezone="America/New_York",
    )

    # Simulate duplicate event (IntegrityError on save)
    mock_uow.agent_events.save.side_effect = DuplicateRecordError(
        "uq_agent_event_hash",
        context={"event_hash": "abc123"},
    )
    # Act
    result = await handler.handle(command)

    # Assert
    assert result.success is True
    assert result.is_duplicate is True
    assert result.event_hash is not None
    assert result.event_type == "Login"

    # Verify rollback was called
    mock_uow.rollback.assert_called_once()


@pytest.mark.asyncio
async def test_process_agent_event_with_ring_group(mock_uow, sample_acd_login_xml):
    """Test processing event with ring group."""
    # Arrange
    handler = ProcessAgentEventCommandHandler(mock_uow)
    command = ProcessAgentEventCommand(
        xml_content=sample_acd_login_xml,
        sqs_message_id="test-message-123",
        client_timezone="America/New_York",
    )

    # Act
    result = await handler.handle(command)

    # Assert
    assert result.success is True
    assert result.is_duplicate is False

    # Verify ring group was created
    mock_uow.ring_groups.get_by_tenant_and_name.assert_called_once()
    mock_uow.ring_groups.create.assert_called_once()


@pytest.mark.asyncio
async def test_process_agent_event_existing_tenant(mock_uow, sample_login_xml):
    """Test processing event with existing tenant."""
    # Arrange
    handler = ProcessAgentEventCommandHandler(mock_uow)
    command = ProcessAgentEventCommand(
        xml_content=sample_login_xml,
        sqs_message_id="test-message-123",
        client_timezone="America/New_York",
    )

    # Mock existing tenant
    mock_uow.tenants.get_by_name.return_value = MagicMock(tenant_key=1)

    # Act
    result = await handler.handle(command)

    # Assert
    assert result.success is True

    # Verify tenant was NOT created (already exists)
    mock_uow.tenants.create.assert_not_called()


@pytest.mark.asyncio
async def test_process_agent_event_existing_agent(mock_uow, sample_login_xml):
    """Test processing event with existing agent."""
    # Arrange
    handler = ProcessAgentEventCommandHandler(mock_uow)
    command = ProcessAgentEventCommand(
        xml_content=sample_login_xml,
        sqs_message_id="test-message-123",
        client_timezone="America/New_York",
    )

    # Mock existing agent
    mock_uow.agents.get_by_tenant_and_name.return_value = MagicMock(agent_key=1)

    # Act
    result = await handler.handle(command)

    # Assert
    assert result.success is True

    # Verify agent was NOT created (already exists)
    mock_uow.agents.create.assert_not_called()


@pytest.mark.asyncio
async def test_process_agent_event_invalid_xml(mock_uow):
    """Test handling of invalid XML."""
    # Arrange
    handler = ProcessAgentEventCommandHandler(mock_uow)
    command = ProcessAgentEventCommand(
        xml_content="<invalid>xml",
        sqs_message_id="test-message-123",
        client_timezone="America/New_York",
    )

    # Act
    result = await handler.handle(command)

    # Assert - handler catches exceptions and returns error result
    assert result.success is False
    assert result.error_message is not None
    assert "Failed to parse XML" in result.error_message


@pytest.mark.asyncio
async def test_process_agent_event_missing_required_fields(mock_uow):
    """Test handling of XML with missing required fields."""
    # Arrange
    handler = ProcessAgentEventCommandHandler(mock_uow)
    xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<LogEvent>
    <eventType>Login</eventType>
</LogEvent>"""

    command = ProcessAgentEventCommand(
        xml_content=xml_content,
        sqs_message_id="test-message-123",
        client_timezone="America/New_York",
    )

    # Act
    result = await handler.handle(command)

    # Assert - handler catches exceptions and returns error result
    assert result.success is False
    assert result.error_message is not None
    assert "Missing required fields" in result.error_message


@pytest.mark.asyncio
async def test_process_agent_event_timezone_conversion(mock_uow, sample_login_xml):
    """Test timezone conversion from UTC to client timezone."""
    # Arrange
    handler = ProcessAgentEventCommandHandler(mock_uow)
    command = ProcessAgentEventCommand(
        xml_content=sample_login_xml,
        sqs_message_id="test-message-123",
        client_timezone="America/Los_Angeles",
    )

    # Act
    result = await handler.handle(command)

    # Assert
    assert result.success is True

    # Verify event was saved with correct timezone
    call_args = mock_uow.agent_events.save.call_args
    agent_event = call_args[0][0]

    # Verify timestamps represent the same instant but in different timezones
    # The hour values should be different (UTC vs local)
    assert agent_event.timestamp_utc.hour != agent_event.timestamp_local.hour
    assert agent_event.timezone_name == "America/Los_Angeles"
    # Verify they represent the same instant in time
    assert agent_event.timestamp_utc == agent_event.timestamp_local


@pytest.mark.asyncio
async def test_process_agent_event_hash_generation(mock_uow, sample_login_xml):
    """Test that event hash is generated correctly."""
    # Arrange
    handler = ProcessAgentEventCommandHandler(mock_uow)
    command = ProcessAgentEventCommand(
        xml_content=sample_login_xml,
        sqs_message_id="test-message-123",
        client_timezone="America/New_York",
    )

    # Act
    result = await handler.handle(command)

    # Assert
    assert result.event_hash is not None
    assert len(result.event_hash) == 64  # SHA-256 produces 64 hex characters

    # Process same event again - should produce same hash
    result2 = await handler.handle(command)
    assert result2.event_hash == result.event_hash
