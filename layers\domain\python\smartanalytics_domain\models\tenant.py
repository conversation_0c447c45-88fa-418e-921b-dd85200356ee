"""Tenant domain entity."""

from dataclasses import dataclass
from datetime import datetime


@dataclass(frozen=True)
class Tenant:
    """Tenant domain entity representing a client organization.

    This is an immutable value object representing a tenant in the system.
    """

    tenant_key: int
    tenant_name: str
    timezone_name: str
    created_at: datetime

    def __post_init__(self) -> None:
        """Validate tenant data."""
        if not self.tenant_name:
            raise ValueError("Tenant name cannot be empty")
        if not self.timezone_name:
            raise ValueError("Timezone name cannot be empty")
