"""Base domain exception classes."""

from typing import Any


class DomainError(Exception):
    """Base exception for all domain-level errors.

    This is the root exception class for all domain-specific errors.
    It provides a consistent interface for error handling and includes
    support for error codes and additional context information.
    """

    def __init__(
        self,
        message: str,
        error_code: str | None = None,
        context: dict[str, Any] | None = None,
    ):
        """Initialize domain error.

        Args:
            message: Human-readable error message
            error_code: Optional error code for programmatic handling
            context: Optional context information for debugging
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.context = context or {}

    def to_dict(self) -> dict[str, Any]:
        """Convert exception to dictionary representation.

        Returns:
            Dictionary with error information
        """
        return {
            "error_type": self.__class__.__name__,
            "error_code": self.error_code,
            "message": self.message,
            "context": self.context,
        }

    def __str__(self) -> str:
        """String representation of the error."""
        if self.context:
            context_str = ", ".join(f"{k}={v}" for k, v in self.context.items())
            return f"{self.message} (context: {context_str})"
        return self.message

    def __repr__(self) -> str:
        """Detailed string representation of the error."""
        return (
            f"{self.__class__.__name__}(message={self.message!r}, "
            f"error_code={self.error_code!r}, context={self.context!r})"
        )
