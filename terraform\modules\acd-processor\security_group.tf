# Security group for Lambda function to access Aurora PostgreSQL, SQS, CloudWatch, S3, and other AWS services
resource "aws_security_group" "acd_processor_security_group" {
  name        = "${var.name_prefix}-acd-processor-sg"
  description = "Security group for ACD processor Lambda function - allows access to Aurora PostgreSQL, AWS services"
  vpc_id      = var.vpc_id

  # Allow PostgreSQL access
  ingress {
    description = "Allow PostgreSQL access within VPC"
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = [data.aws_vpc.selected.cidr_block]
  }

  # Allow HTTPS access within VPC for internal services
  ingress {
    description = "Allow HTTPS access within VPC"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [data.aws_vpc.selected.cidr_block]
  }

  # Allow all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-acd-processor-sg"
    }
  )
}
