"""Tests for exception classes."""

from smartanalytics_domain.exceptions.base import DomainError
from smartanalytics_domain.exceptions.event_exceptions import EventProcessingError


class TestExceptions:
    """Test exception classes."""

    def test_domain_error(self):
        """Test base DomainError."""
        exc = DomainError("Test error")
        assert str(exc) == "Test error"
        assert isinstance(exc, Exception)

    def test_domain_error_with_context(self):
        """Test DomainError with context."""
        exc = DomainError("Test error", context={"key": "value"})
        assert "Test error" in str(exc)
        assert exc.context == {"key": "value"}

    def test_domain_error_to_dict(self):
        """Test DomainError to_dict method."""
        exc = DomainError(
            "Test error", error_code="TEST_ERROR", context={"key": "value"}
        )
        result = exc.to_dict()
        assert result["error_type"] == "DomainError"
        assert result["error_code"] == "TEST_ERROR"
        assert result["message"] == "Test error"
        assert result["context"] == {"key": "value"}

    def test_event_processing_error(self):
        """Test EventProcessingError."""
        exc = EventProcessingError("Processing failed")
        assert str(exc) == "Processing failed"
        assert isinstance(exc, DomainError)

    def test_event_processing_error_with_event_data(self):
        """Test EventProcessingError with event data."""
        event_data = {"tenant": "test", "agent": "agent1"}
        exc = EventProcessingError("Failed", event_data=event_data)
        assert "Failed" in str(exc)
        assert exc.context["event_data"] == event_data
        assert isinstance(exc, EventProcessingError)
