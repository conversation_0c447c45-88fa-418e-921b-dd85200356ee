variables:
  # Project configuration
  DOCKER_PYTHON_TAG: python:3.12-slim
  PACKAGE_REGISTRY_URL: "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/generic"
  PACKAGE_NAME: "smartanalytics-processors"

  # Cache configuration
  PIP_CACHE_DIR: "${CI_PROJECT_DIR}/.cache/pip"
  RUFF_CACHE_DIR: "${CI_PROJECT_DIR}/.cache/ruff"
  MYPY_CACHE_DIR: "${CI_PROJECT_DIR}/.cache/mypy"

  # Lambda configuration - Add new lambdas here
  LAMBDA_FUNCTIONS: "acd-event-processor"

  # Layer configuration - Add new layers here
  LAMBDA_LAYERS: "utilities domain infrastructure"

  # Quality gates
  COVERAGE_THRESHOLD: "50"

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
      when: never
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_TAG =~ /^rel-\d+\.\d+\.\d+$/

default:
  image: $DOCKER_PYTHON_TAG
  cache:
    key:
      files:
        - "**/requirements*.txt"
        - "**/pyproject.toml"
    paths:
      - ${PIP_CACHE_DIR}
      - ${RUFF_CACHE_DIR}
      - ${MYPY_CACHE_DIR}

.before_script_template: &before_script_template
  before_script:
    - set -euo pipefail
    - apt-get update -qq && apt-get install -y -qq git curl zip unzip jq bc
    - python --version
    - pip install --upgrade pip
    - mkdir -p ${PIP_CACHE_DIR} ${RUFF_CACHE_DIR} ${MYPY_CACHE_DIR}
    - pip install -r requirements-ci.txt

stages:
  - validate
  - test
  - build-layers
  - build
  - publish

# Validation stage - check code quality and formatting
validate:layers:
  stage: validate
  <<: *before_script_template
  script:
    - echo "Validating all Lambda layers..."

    # First, install all layers as editable packages to resolve cross-layer dependencies
    - echo "Installing all layers for cross-layer dependency resolution..."
    - |
      for layer in $LAMBDA_LAYERS; do
        echo "Installing layer: $layer..."
        cd layers/$layer/python

        # Install layer dependencies
        if [ -f "../requirements.txt" ]; then
          pip install -r ../requirements.txt
        fi
        pip install -e .

        cd ../../..
      done

    # Now validate each layer with all dependencies available
    - |
      for layer in $LAMBDA_LAYERS; do
        echo "Validating layer: $layer..."
        cd layers/$layer/python

        # Run code quality checks
        echo "Running ruff formatter check..."
        ruff format --check --diff .

        echo "Running ruff linting..."
        ruff check .

        echo "Running mypy type checking..."
        mypy smartanalytics_${layer} --config-file=../../../pyproject.toml

        echo "Running bandit security check..."
        bandit -r . -c ../../../pyproject.toml

        cd ../../..
      done
  rules:
    - changes:
        - layers/**/*
        - requirements*.txt
        - pyproject.toml
        - .gitlab-ci.yml
    # Run on MR pipelines
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    # Run on direct pushes to develop
    - if: $CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push"
    # Run on release tags
    - if: $CI_COMMIT_TAG =~ /^rel-\d+\.\d+\.\d+$/
    # Otherwise, don't run
    - when: never

validate:lambdas:
  stage: validate
  <<: *before_script_template
  script:
    - echo "Validating all Lambda functions..."
    - |
      for lambda_func in $LAMBDA_FUNCTIONS; do
        echo "Validating $lambda_func..."
        cd lambdas/$lambda_func

        # Install function-specific dependencies
        pip install -r requirements.txt
        pip install -r requirements-dev.txt

        # Run code quality checks
        echo "Running ruff formatter check..."
        ruff format --check --diff .

        echo "Running ruff linting..."
        ruff check .

        echo "Running mypy type checking..."
        export MYPY_CACHE_DIR=${MYPY_CACHE_DIR}
        # Run mypy from root directory to find layer modules
        cd ../..
        mypy lambdas/$lambda_func/src --config-file=pyproject.toml --no-incremental
        cd lambdas/$lambda_func

        echo "Running bandit security check..."
        bandit -r . -c ../../pyproject.toml

        cd ../..
      done
  rules:
    - changes:
        - lambdas/**/*
        - requirements*.txt
        - pyproject.toml
        - .gitlab-ci.yml
    # Run on MR pipelines
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    # Run on direct pushes to develop
    - if: $CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push"
    # Run on release tags
    - if: $CI_COMMIT_TAG =~ /^rel-\d+\.\d+\.\d+$/
    # Otherwise, don't run
    - when: never

# Test stage - run unit and integration tests
.test_template: &test_template
  stage: test
  <<: *before_script_template
  coverage: '/TOTAL.*\s+(\d+%)$/'
  artifacts:
    reports:
      junit: lambdas/${LAMBDA_FUNCTION}/test-results.xml
      coverage_report:
        coverage_format: cobertura
        path: lambdas/${LAMBDA_FUNCTION}/coverage.xml
    paths:
      - lambdas/${LAMBDA_FUNCTION}/htmlcov/
      - lambdas/${LAMBDA_FUNCTION}/test-results.xml
      - lambdas/${LAMBDA_FUNCTION}/coverage.xml
    expire_in: 1 week
  script:
    - echo "Testing $LAMBDA_FUNCTION..."

    # Install all layers first (Lambda functions depend on layers)
    - |
      echo "Installing layer dependencies..."
      # Install utilities layer
      cd layers/utilities/python
      if [ -f "../requirements.txt" ]; then pip install -r ../requirements.txt; fi
      pip install -e .
      cd ../../..

      # Install domain layer
      cd layers/domain/python
      pip install -e .
      cd ../../..

      # Install infrastructure layer
      cd layers/infrastructure/python
      if [ -f "../requirements.txt" ]; then pip install -r ../requirements.txt; fi
      pip install -e .
      cd ../../..

    - cd lambdas/$LAMBDA_FUNCTION

    # Install Lambda dependencies
    - pip install -r requirements.txt
    - pip install -r requirements-dev.txt

    # Run tests with coverage
    - pytest tests/ --junitxml=test-results.xml --cov=src --cov-report=xml --cov-report=html --cov-report=term

    # Check coverage threshold
    - coverage report --fail-under=${COVERAGE_THRESHOLD}

    - cd ../..

# Layer test template
.layer_test_template: &layer_test_template
  stage: test
  <<: *before_script_template
  coverage: '/TOTAL.*\s+(\d+%)$/'
  artifacts:
    reports:
      junit: layers/${LAYER_NAME}/python/test-results.xml
      coverage_report:
        coverage_format: cobertura
        path: layers/${LAYER_NAME}/python/coverage.xml
    paths:
      - layers/${LAYER_NAME}/python/htmlcov/
      - layers/${LAYER_NAME}/python/test-results.xml
      - layers/${LAYER_NAME}/python/coverage.xml
    expire_in: 1 week
  script:
    - |
      echo "Testing layer: $LAYER_NAME..."

    # Install layer dependencies in correct order
    - |
      # Install utilities layer first (no dependencies)
      if [ "$LAYER_NAME" != "utilities" ]; then
        echo "Installing utilities layer dependency..."
        cd layers/utilities/python
        if [ -f "../requirements.txt" ]; then pip install -r ../requirements.txt; fi
        pip install -e .
        cd ../../..
      fi

    # Install domain layer (depends on utilities)
    - |
      if [ "$LAYER_NAME" = "infrastructure" ]; then
        echo "Installing domain layer dependency..."
        cd layers/domain/python
        pip install -e .
        cd ../../..
      fi

    # Install and test the target layer
    - |
      cd layers/$LAYER_NAME/python

      # Install layer dependencies
      if [ -f "../requirements.txt" ]; then pip install -r ../requirements.txt; fi
      pip install -e .

      # Run tests with coverage
      pytest tests/ --junitxml=test-results.xml --cov=smartanalytics_${LAYER_NAME} --cov-report=xml --cov-report=html --cov-report=term

      # Check coverage threshold
      coverage report --fail-under=${COVERAGE_THRESHOLD}

test:utilities-layer:
  <<: *layer_test_template
  variables:
    LAYER_NAME: "utilities"
  rules:
    - changes:
        - layers/utilities/**
        - requirements*.txt
        - pyproject.toml
        - .gitlab-ci.yml
    # Run on MR pipelines
    # Run on MR pipelines
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    # Run on direct pushes to develop
    - if: $CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push"
    # Run on release tags
    - if: $CI_COMMIT_TAG =~ /^rel-\d+\.\d+\.\d+$/
    # Otherwise, don't run
    - when: never

test:domain-layer:
  <<: *layer_test_template
  variables:
    LAYER_NAME: "domain"
  needs:
    - job: test:utilities-layer
      artifacts: false
  rules:
    - changes:
        - layers/domain/**
        - requirements*.txt
        - pyproject.toml
        - .gitlab-ci.yml
    # Run on MR pipelines
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    # Run on direct pushes to develop
    - if: $CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push"
    # Run on release tags
    - if: $CI_COMMIT_TAG =~ /^rel-\d+\.\d+\.\d+$/
    # Otherwise, don't run
    - when: never

test:infrastructure-layer:
  <<: *layer_test_template
  variables:
    LAYER_NAME: "infrastructure"
  needs:
    - job: test:utilities-layer
      artifacts: false
    - job: test:domain-layer
      artifacts: false
  rules:
    - changes:
        - layers/infrastructure/**
        - requirements*.txt
        - pyproject.toml
        - .gitlab-ci.yml
    # Run on MR pipelines
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    # Run on direct pushes to develop
    - if: $CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push"
    # Run on release tags
    - if: $CI_COMMIT_TAG =~ /^rel-\d+\.\d+\.\d+$/
    # Otherwise, don't run
    - when: never

test:acd-event-processor:
  <<: *test_template
  variables:
    LAMBDA_FUNCTION: "acd-event-processor"
  needs:
    - job: test:utilities-layer
      artifacts: false
    - job: test:domain-layer
      artifacts: false
    - job: test:infrastructure-layer
      artifacts: false
  rules:
    - changes:
        - lambdas/acd-event-processor/**
        - requirements*.txt
        - pyproject.toml
        - .gitlab-ci.yml
    # Run on MR pipelines
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    # Run on direct pushes to develop
    - if: $CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push"
    # Run on release tags
    - if: $CI_COMMIT_TAG =~ /^rel-\d+\.\d+\.\d+$/
    # Otherwise, don't run
    - when: never

# Build Layers stage - create Lambda layer packages
build:layers:
  stage: build-layers
  <<: *before_script_template
  artifacts:
    paths:
      - build/*.zip
    expire_in: 1 week
  script:
    - echo "Building Lambda layers..."

    # Create build directory
    - mkdir -p build

    # Build utilities layer
    - echo "Building utilities layer..."
    - mkdir -p build/utilities/python
    - cp -r layers/utilities/python/* build/utilities/python/
    - pip install -r layers/utilities/requirements.txt -t build/utilities/python --upgrade
    - cd build/utilities && zip -r ../utilities-layer.zip python -x "*.pyc" "*/__pycache__/*" && cd ../..

    # Build domain layer
    - echo "Building domain layer..."
    - mkdir -p build/domain/python
    - cp -r layers/domain/python/* build/domain/python/
    - if [ -f layers/domain/requirements.txt ]; then pip install -r layers/domain/requirements.txt -t build/domain/python --upgrade; fi
    - cd build/domain && zip -r ../domain-layer.zip python -x "*.pyc" "*/__pycache__/*" && cd ../..

    # Build infrastructure layer
    - echo "Building infrastructure layer..."
    - mkdir -p build/infrastructure/python
    - cp -r layers/infrastructure/python/* build/infrastructure/python/
    - pip install -r layers/infrastructure/requirements.txt -t build/infrastructure/python --upgrade
    - cd build/infrastructure && zip -r ../infrastructure-layer.zip python -x "*.pyc" "*/__pycache__/*" && cd ../..

    # List built layers
    - echo "Built layers:"
    - ls -lh build/*.zip
    - du -sh build/*.zip
  rules:
    - changes:
        - layers/**/*
        - layers/**/requirements.txt
        - .gitlab-ci.yml
    # Run on MR pipelines
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    # Run on direct pushes to develop
    - if: $CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push"
    # Run on release tags
    - if: $CI_COMMIT_TAG =~ /^rel-\d+\.\d+\.\d+$/
    # Otherwise, don't run
    - when: never

# Build stage - create Lambda deployment packages
.build_template: &build_template
  stage: build
  <<: *before_script_template
  artifacts:
    paths:
      - "*.zip"
    expire_in: 1 week
  script:
    - echo "Building $LAMBDA_FUNCTION..."
    - cd lambdas/$LAMBDA_FUNCTION

    # Create build directory
    - mkdir -p build
    - cd build

    # Copy source code (Lambda handler only - layers provide dependencies)
    - cp -r ../src/* .

    # Install ONLY Lambda-specific dependencies (not layer dependencies)
    # Layer dependencies are provided by Lambda layers
    - pip install -r ../requirements.txt -t . --upgrade

    # Create deployment package
    - zip -r "../../../${LAMBDA_FUNCTION}.zip" . -x "*.pyc" "*/__pycache__/*"

    - cd ../../..
    - ls -la
    - du -sh ${LAMBDA_FUNCTION}.zip
    - unzip -l "${LAMBDA_FUNCTION}.zip" | head -n 10 || true
    - echo "Build completed successfully!"
    - |
      echo "Package size: $(du -sh ${LAMBDA_FUNCTION}.zip | cut -f1)"
      echo "Number of files: $(unzip -l ${LAMBDA_FUNCTION}.zip | wc -l)"
      echo "Note: Lambda uses layers for most dependencies - package should be small"

build:acd-event-processor:
  <<: *build_template
  variables:
    LAMBDA_FUNCTION: "acd-event-processor"
  rules:
    - changes:
        - lambdas/acd-event-processor/**
        - requirements*.txt
        - pyproject.toml
        - .gitlab-ci.yml
    # Run on MR pipelines
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    # Run on direct pushes to develop
    - if: $CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push"
    # Run on release tags
    - if: $CI_COMMIT_TAG =~ /^rel-\d+\.\d+\.\d+$/
    # Otherwise, don't run
    - when: never

# Publish stage - upload packages to GitLab Package Registry
publish:
  stage: publish
  <<: *before_script_template
  rules:
    - changes:
        - lambdas/**/*
        - requirements*.txt
        - pyproject.toml
        - .gitlab-ci.yml
    # Hard block outside the expected namespace
    - if: $CI_PROJECT_NAMESPACE != "data-analytics"
      when: never
    # Publish on direct pushes to develop
    - if: $CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push"
    # Publish on release tags
    - if: $CI_COMMIT_TAG =~ /^rel-\d+\.\d+\.\d+$/
    # Otherwise, don't run
    - when: never
  script:
    - |
      set -euo pipefail
      echo "Publishing Lambda packages and layers..."

      # Safe version selection with nounset enabled
      PACKAGE_VERSION="${CI_COMMIT_TAG:-latest}"
      echo "Publishing version: ${PACKAGE_VERSION}"

      any_uploaded=false

      # Upload Lambda functions
      for lambda_func in $LAMBDA_FUNCTIONS; do
        zip_path="${lambda_func}.zip"
        if [[ -f "${zip_path}" ]]; then
          echo "Uploading Lambda function: ${zip_path}..."
          curl --fail --header "JOB-TOKEN: ${CI_JOB_TOKEN}" \
              --upload-file "${zip_path}" \
              "${PACKAGE_REGISTRY_URL}/${PACKAGE_NAME}/${PACKAGE_VERSION}/${lambda_func}.zip"
          any_uploaded=true
        else
          echo "Warning: ${zip_path} not found"
        fi
      done

      # Upload Lambda layers
      for layer_zip in build/utilities-layer.zip build/domain-layer.zip build/infrastructure-layer.zip; do
        if [[ -f "${layer_zip}" ]]; then
          layer_name=$(basename "${layer_zip}")
          echo "Uploading Lambda layer: ${layer_name}..."
          curl --fail --header "JOB-TOKEN: ${CI_JOB_TOKEN}" \
              --upload-file "${layer_zip}" \
              "${PACKAGE_REGISTRY_URL}/${PACKAGE_NAME}/${PACKAGE_VERSION}/${layer_name}"
          any_uploaded=true
        else
          echo "Warning: ${layer_zip} not found"
        fi
      done

      # Write manifest (even if none uploaded, so artifacts step never complains)
      cat > package-manifest.json << EOF
      {
        "package_name": "${PACKAGE_NAME}",
        "version": "${PACKAGE_VERSION}",
        "commit": "${CI_COMMIT_SHA}",
        "pipeline_id": "${CI_PIPELINE_ID}",
        "created_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
        "functions": [
      EOF

      first=true
      for lambda_func in $LAMBDA_FUNCTIONS; do
        zip_path="${lambda_func}.zip"
        if [[ -f "${zip_path}" ]]; then
          $first || echo "," >> package-manifest.json
          first=false
          echo "      {" >> package-manifest.json
          echo "        \"name\": \"${lambda_func}\"," >> package-manifest.json
          echo "        \"filename\": \"${lambda_func}.zip\"," >> package-manifest.json
          echo "        \"size\": $(stat -c%s "${zip_path}" 2>/dev/null || stat -f%z "${zip_path}" 2>/dev/null || echo "0")" >> package-manifest.json
          echo "      }" >> package-manifest.json
        fi
      done
      echo "    ] }" >> package-manifest.json

      # dotenv for downstream jobs (always present)
      {
        echo "PACKAGE_NAME=${PACKAGE_NAME}"
        echo "PACKAGE_VERSION=${PACKAGE_VERSION}"
        echo "PACKAGE_MANIFEST=package-manifest.json"
      } > package-info.env

      # Upload manifest
      curl --fail --header "JOB-TOKEN: ${CI_JOB_TOKEN}" \
          --upload-file "package-manifest.json" \
          "${PACKAGE_REGISTRY_URL}/${PACKAGE_NAME}/${PACKAGE_VERSION}/package-manifest.json"

      # Optional: fail if nothing was uploaded (comment out to just warn)
      if [[ "${any_uploaded}" != "true" ]]; then
        echo "No lambda zip files found to publish."
        # exit 1
      fi
  artifacts:
    reports:
      dotenv: package-info.env
    paths:
      - package-manifest.json
    expire_in: 1 week
  needs:
    - job: build:layers
      artifacts: true
    - job: build:acd-event-processor
      artifacts: true
