# Infrastructure Layer

The Infrastructure Layer provides concrete implementations of the domain layer's ports/interfaces. It handles all external concerns including database access, AWS services, and third-party integrations.

## Architecture

```mermaid
graph TB
    subgraph "Infrastructure Layer"
        direction LR
        A[SQLAlchemy Models] --> B[Repository Implementations]
        B --> C[Unit of Work]
        C --> D[Database Connection]
        D --> E[Session Management]
    end
```

## Components

### Database Models (`models/`)
SQLAlchemy ORM models for Aurora PostgreSQL:
- `DimTenant` - Tenant dimension table
- `DimAgent` - Agent dimension table
- `DimRingGroup` - Ring group dimension table
- `DimAgentEvent` - Agent event fact table

### Database Connection (`database/`)
Database connectivity and session management:
- `connection.py` - Database engine and connection management
- `session.py` - SQLAlchemy session factory and lifecycle

### Repositories (`repositories/`)
Concrete implementations of domain ports:
- `SqlAlchemyTenantRepository` - Tenant data access
- `SqlAlchemyAgentRepository` - Agent data access
- `SqlAlchemyRingGroupRepository` - Ring group data access
- `SqlAlchemyAgentEventRepository` - Event data access

### Unit of Work (`unit_of_work.py`)
Transaction management and repository coordination:
- `SqlAlchemyUnitOfWork` - Database transaction management
- Ensures ACID properties across multiple repository operations

## Database Schema

```mermaid
erDiagram
    DimTenant {
        bigint tenant_key PK
        varchar tenant_name
        varchar timezone_name
        timestamp created_at_utc
    }

    DimAgent {
        bigint agent_key PK
        bigint tenant_key FK
        varchar agent_name
        varchar agent_role
        timestamp created_at_utc
    }

    DimRingGroup {
        bigint ring_group_key PK
        bigint tenant_key FK
        varchar ring_group_name
        varchar ring_group_uri
        timestamp created_at_utc
    }

    DimAgentEvent {
        bigint event_key PK
        bigint agent_key FK
        bigint ring_group_key FK
        bigint tenant_key FK
        varchar event_type
        varchar hash_event
        timestamp timestamp_utc
        timestamp timestamp_local
        varchar operator_id
        varchar workstation
        varchar media_label
        varchar uri
        varchar device_name
        varchar busied_out_action
        int busied_out_duration
        varchar reason_code
        jsonb event_data
        timestamp created_at_utc
    }

    DimTenant ||--o{ DimAgent : "has"
    DimTenant ||--o{ DimRingGroup : "has"
    DimTenant ||--o{ DimAgentEvent : "belongs_to"
    DimAgent ||--o{ DimAgentEvent : "performs"
    DimRingGroup ||--o{ DimAgentEvent : "involves"
```

## Configuration

### Database Connection
Supports multiple connection methods:
- **Password Authentication**: Username/password
- **IAM Authentication**: AWS IAM roles (via RDS Proxy)
- **Connection Pooling**: Optimized for Lambda containers

### Environment Variables
```bash
# Database Configuration
DATABASE_HOST=your-aurora-cluster.cluster-xxx.region.rds.amazonaws.com
DATABASE_PORT=5432
DATABASE_NAME=postgres
DATABASE_USERNAME=your_username
DATABASE_PASSWORD=your_password  # Optional with IAM auth
DATABASE_SCHEMA_NAME=your_schema
DATABASE_SSL_MODE=require

# Connection Settings
DATABASE_POOL_SIZE=5
DATABASE_MAX_OVERFLOW=10
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600
```

## Usage Example

```python
from smartanalytics_infrastructure.unit_of_work import SqlAlchemyUnitOfWork
from smartanalytics_infrastructure.database.connection import create_engine

# Create database engine
engine = await create_engine()

# Use Unit of Work pattern
async with SqlAlchemyUnitOfWork(engine) as uow:
    # Create tenant
    tenant = await uow.tenants.create("TestTenant", "America/Chicago")

    # Create agent
    agent = await uow.agents.create("agent001", tenant.tenant_key, "Agent")

    # Commit transaction
    await uow.commit()
```

## Dependencies

```txt
asyncpg>=0.29.0          # PostgreSQL async driver
sqlalchemy>=2.0.0        # ORM framework
psycopg2-binary>=2.9.0   # PostgreSQL sync driver (fallback)
```

## Performance Optimizations

### Lambda Container Optimization
- **Connection Reuse**: Global engine initialization
- **Connection Pooling**: NullPool for Lambda (single connection)
- **Async Operations**: Non-blocking database operations

### Query Optimization
- **Bulk Operations**: Batch inserts for multiple events
- **Prepared Statements**: SQLAlchemy query optimization
- **Index Usage**: Optimized queries for dimensional lookups

## Testing

Infrastructure layer testing includes:
- Repository unit tests with mocked sessions
- Database integration tests
- Connection management tests
- Transaction rollback tests

## Integration

The infrastructure layer:
- **Implements**: Domain layer ports/interfaces
- **Used by**: Domain command handlers
- **Connects to**: Aurora PostgreSQL database
- **Manages**: Database transactions and sessions
