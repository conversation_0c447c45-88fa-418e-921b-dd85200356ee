"""Ports (interfaces) for the domain layer.

Ports define the contracts between the domain layer and external systems.
They are implemented by the infrastructure layer and used by the domain
services to interact with external dependencies like databases, message
queues, and external APIs.

This follows the hexagonal architecture pattern where the domain layer
defines what it needs (ports) and the infrastructure layer provides
the implementations (adapters).
"""

from smartanalytics_domain.ports.repositories import AgentEventRepository
from smartanalytics_domain.ports.unit_of_work import UnitOfWork

__all__ = [
    # Repository interfaces
    "AgentEventRepository",
    # Unit of work interface
    "UnitOfWork",
]
