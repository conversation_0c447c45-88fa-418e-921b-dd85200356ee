# Ruff configuration for Smart Analytics Processors
# https://docs.astral.sh/ruff/configuration/

# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

# Same as Black.
line-length = 88
indent-width = 4

# Assume Python 3.12
target-version = "py312"

[lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`)  codes by default.
# Unlike Flake8, Ruff doesn't enable pycodestyle warnings (`W`) or
# McCabe complexity (`C901`) by default.
select = [
    "E4",      # pycodestyle errors
    "E7",      # pycodestyle errors
    "E9",      # pycodestyle errors
    "F",       # Pyflakes
    "W",       # pycodestyle warnings
    "B",       # flake8-bugbear
    "I",       # isort
    "N",       # pep8-naming
    "UP",      # pyupgrade
    "YTT",     # flake8-2020
    "S",       # flake8-bandit
    "BLE",     # flake8-blind-except
    "FBT",     # flake8-boolean-trap
    "A",       # flake8-builtins
    "COM",     # flake8-commas
    "C4",      # flake8-comprehensions
    "DTZ",     # flake8-datetimez
    "T10",     # flake8-debugger
    "DJ",      # flake8-django
    "EM",      # flake8-errmsg
    "EXE",     # flake8-executable
    "FA",      # flake8-future-annotations
    "ISC",     # flake8-implicit-str-concat
    "ICN",     # flake8-import-conventions
    "G",       # flake8-logging-format
    "INP",     # flake8-no-pep420
    "PIE",     # flake8-pie
    "T20",     # flake8-print
    "PYI",     # flake8-pyi
    "PT",      # flake8-pytest-style
    "Q",       # flake8-quotes
    "RSE",     # flake8-raise
    "RET",     # flake8-return
    "SLF",     # flake8-self
    "SLOT",    # flake8-slots
    "SIM",     # flake8-simplify
    "TID",     # flake8-tidy-imports
    "TCH",     # flake8-type-checking
    "INT",     # flake8-gettext
    "ARG",     # flake8-unused-arguments
    "PTH",     # flake8-use-pathlib
    "TD",      # flake8-todos
    "FIX",     # flake8-fixme
    "ERA",     # eradicate
    "PD",      # pandas-vet
    "PGH",     # pygrep-hooks
    "PL",      # Pylint
    "TRY",     # tryceratops
    "FLY",     # flynt
    "NPY",     # NumPy-specific rules
    "PERF",    # Perflint
    "FURB",    # refurb
    "LOG",     # flake8-logging
    "RUF",     # Ruff-specific rules
]

ignore = [
    # Security - Allow for development/testing
    "S101",    # Use of assert detected
    "S104",    # Possible binding to all interfaces
    "S106",    # Possible hardcoded password
    "S108",    # Probable insecure usage of temp file/directory
    "S311",    # Standard pseudo-random generators are not suitable for security/cryptographic purposes

    # Complexity - Allow for now, improve gradually
    "PLR0911", # Too many return statements
    "PLR0913", # Too many arguments to function call
    "PLR0915", # Too many statements
    "PLR2004", # Magic value used in comparison

    # Formatting conflicts
    "COM812",  # Trailing comma missing (conflicts with formatter)
    "ISC001",  # Implicitly concatenated string literals on one line (conflicts with formatter)

    # TODOs and FIXMEs - Allow for development
    "TD002",   # Missing author in TODO
    "TD003",   # Missing issue link on the line following this TODO
    "FIX002",  # Line contains TODO, consider resolving the issue

    # Exception handling - Allow for now, improve gradually
    "TRY002",  # Create your own exception
    "TRY003",  # Avoid specifying long messages outside the exception class
    "TRY004",  # Prefer TypeError exception for invalid type
    "TRY201",  # Use raise without specifying exception name
    "TRY300",  # Consider moving this statement to an else block
    "TRY301",  # Abstract raise to an inner function
    "TRY400",  # Use logging.exception instead of logging.error
    "BLE001",  # Do not catch blind exception: Exception

    # Error messages - Allow for now
    "EM101",   # Exception must not use a string literal
    "EM102",   # Exception must not use an f-string literal

    # Logging - Allow f-strings in logging for now
    "G004",    # Logging statement uses f-string
    "G201",    # Use logging.exception instead of logging.error(..., exc_info=True)

    # Type annotations - Allow for gradual improvement
    "RUF013",  # PEP 484 prohibits implicit Optional

    # Datetime - Allow for now, improve gradually
    "DTZ001",  # datetime.datetime() called without a tzinfo argument
    "DTZ005",  # datetime.datetime.now() called without a tz argument
    "DTZ007",  # Naive datetime constructed using datetime.strptime() without %z

    # Function arguments - Allow for now
    "ARG002",  # Unused method argument
    "FBT001",  # Boolean positional arg in function definition
    "FBT002",  # Boolean default value in function definition
    "FBT003",  # Boolean positional value in function call

    # Code style - Allow for now
    "SIM103",  # Return the negated condition directly
    "SIM108",  # Use ternary operator instead of if-else-block
    "SIM117",  # Use a single with statement with multiple contexts
    "SIM401",  # Use dict.get() instead of if block
    "RET504",  # Unnecessary assignment before return statement
    "UP038",   # Use X | Y in isinstance call instead of (X, Y)

    # Path handling - Allow for now
    "PTH118",  # os.path.join() should be replaced by Path with / operator
    "PTH120",  # os.path.dirname() should be replaced by Path.parent

    # Private member access in tests
    "SLF001",  # Private member accessed (allow in tests)

    # Print statements in test scripts
    "T201",    # print found (allow in test scripts)

    # Naming conventions - Allow for now
    "N812",    # Lowercase imported as non-lowercase

    "EXE001",   # Found executable file

    "PLW0603",  # Global statement
]

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

[lint.per-file-ignores]
# Tests can use magic values, assertions, and fixtures
"**/tests/**test_*.py" = [
    "S101",    # Use of assert detected
    "PLR2004", # Magic value used in comparison
    "ARG001",  # Unused function argument (fixtures)
    "FBT001",  # Boolean positional arg in function definition
    "FBT002",  # Boolean default value in function definition
    "S105",    # Test can use hardcoded passwords
    "PT011",   # Test can use broad exception clauses
    "B017",    # Test can use evil except pass
]

# Allow print statements in scripts
"scripts/**/*.py" = ["T201"]

# Allow relative imports in __init__.py files
"**/__init__.py" = ["F401", "F403"]

[lint.isort]
known-first-party = ["src", "agent_event_processor"]
force-single-line = false
combine-as-imports = true

[lint.flake8-pytest-style]
fixture-parentheses = false
mark-parentheses = false

[lint.mccabe]
max-complexity = 10
