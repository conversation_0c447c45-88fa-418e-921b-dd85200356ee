"""Tests for agent event domain model."""

from datetime import datetime

import pytest
from smartanalytics_domain.models.agent_event import (
    Agent<PERSON>vent,
    AgentEventBatch,
    EventType,
)


class TestAgentEvent:
    """Test cases for AgentEvent domain model."""

    def test_create_valid_agent_event(self):
        """Test creating a valid agent event."""
        # Arrange
        tenant_id = "test-tenant"
        agent_id = "test-agent"
        event_hash = "a" * 64  # Valid 64-char hex string
        timestamp_utc = datetime(2024, 1, 1, 12, 0, 0)
        timestamp_local = datetime(2024, 1, 1, 6, 0, 0)

        # Act
        agent_event = AgentEvent(
            tenant_id=tenant_id,
            agent_id=agent_id,
            event_type=EventType.LOGIN,
            event_hash=event_hash,
            timestamp_utc=timestamp_utc,
            timestamp_local=timestamp_local,
            tenant_name="Test Tenant",
            timezone_name="America/Chicago",
            agent_name="Test Agent",
        )

        # Assert
        assert agent_event.tenant_id == tenant_id
        assert agent_event.agent_id == agent_id
        assert agent_event.event_type == EventType.LOGIN
        assert agent_event.event_hash == event_hash
        assert agent_event.timestamp_utc == timestamp_utc
        assert agent_event.timestamp_local == timestamp_local
        assert agent_event.tenant_name == "Test Tenant"
        assert agent_event.agent_name == "Test Agent"
        assert agent_event.json_data == {}  # Default empty dict

    def test_agent_event_validation_empty_tenant_name(self):
        """Test agent event validation with empty tenant name."""
        # Arrange & Act & Assert
        with pytest.raises(ValueError, match="tenant_name is required"):
            AgentEvent(
                tenant_id="test-tenant",
                agent_id="test-agent",
                event_type=EventType.LOGIN,
                event_hash="a" * 64,
                timestamp_utc=datetime.now(),
                timestamp_local=datetime.now(),
                tenant_name="",  # Empty tenant name
                timezone_name="America/Chicago",
                agent_name="Test Agent",
            )

    def test_agent_event_validation_invalid_hash_length(self):
        """Test agent event validation with invalid hash length."""
        # Arrange & Act & Assert
        with pytest.raises(ValueError, match="hash_event must be 64-character SHA-256"):
            AgentEvent(
                tenant_id="test-tenant",
                agent_id="test-agent",
                event_type=EventType.LOGIN,
                event_hash="short_hash",  # Invalid length
                timestamp_utc=datetime.now(),
                timestamp_local=datetime.now(),
                tenant_name="Test Tenant",
                timezone_name="America/Chicago",
                agent_name="Test Agent",
            )

    def test_agent_event_is_login_event(self):
        """Test is_login_event property."""
        # Arrange
        login_event = self._create_test_event(EventType.LOGIN)
        acd_login_event = self._create_test_event(EventType.ACD_LOGIN)
        logout_event = self._create_test_event(EventType.LOGOUT)

        # Act & Assert
        assert login_event.is_login_event is True
        assert acd_login_event.is_login_event is True
        assert logout_event.is_login_event is False

    def test_agent_event_is_logout_event(self):
        """Test is_logout_event property."""
        # Arrange
        logout_event = self._create_test_event(EventType.LOGOUT)
        acd_logout_event = self._create_test_event(EventType.ACD_LOGOUT)
        login_event = self._create_test_event(EventType.LOGIN)

        # Act & Assert
        assert logout_event.is_logout_event is True
        assert acd_logout_event.is_logout_event is True
        assert login_event.is_logout_event is False

    def test_agent_event_has_ring_group(self):
        """Test has_ring_group property."""
        # Arrange
        event_with_ring_group = self._create_test_event(
            EventType.LOGIN,
            ring_group_name="Test Ring Group",
            ring_group_uri="sip:<EMAIL>",
        )
        event_without_ring_group = self._create_test_event(EventType.LOGIN)

        # Act & Assert
        assert event_with_ring_group.has_ring_group is True
        assert event_without_ring_group.has_ring_group is False

    def test_agent_event_to_dict(self):
        """Test conversion to dictionary."""
        # Arrange
        agent_event = self._create_test_event(EventType.LOGIN)

        # Act
        event_dict = agent_event.to_dict()

        # Assert
        assert event_dict["tenant_id"] == "test-tenant"
        assert event_dict["agent_id"] == "test-agent"
        assert event_dict["event_type"] == "Login"
        assert event_dict["tenant_name"] == "Test Tenant"
        assert event_dict["agent_name"] == "Test Agent"

    def _create_test_event(
        self,
        event_type: EventType,
        ring_group_name: str = None,
        ring_group_uri: str = None,
        **kwargs,
    ) -> AgentEvent:
        """Helper method to create test agent event."""
        return AgentEvent(
            tenant_id="test-tenant",
            agent_id="test-agent",
            event_type=event_type,
            event_hash="a" * 64,
            timestamp_utc=datetime(2024, 1, 1, 12, 0, 0),
            timestamp_local=datetime(2024, 1, 1, 6, 0, 0),
            tenant_name="Test Tenant",
            timezone_name="America/Chicago",
            agent_name="Test Agent",
            ring_group_name=ring_group_name,
            ring_group_uri=ring_group_uri,
            **kwargs,
        )


class TestAgentEventBatch:
    """Test cases for AgentEventBatch."""

    def test_create_valid_batch(self):
        """Test creating a valid agent event batch."""
        # Arrange
        events = [
            self._create_test_event(EventType.LOGIN),
            self._create_test_event(EventType.LOGOUT),
        ]

        # Act
        batch = AgentEventBatch(
            events=events, batch_id="test-batch-1", received_at=datetime.now()
        )

        # Assert
        assert batch.size == 2
        assert len(batch.tenant_ids) == 1  # Same tenant
        assert len(batch.agent_ids) == 1  # Same agent
        assert len(batch.event_types) == 2  # Different event types

    def test_batch_validation_empty_events(self):
        """Test batch validation with empty events list."""
        # Arrange & Act & Assert
        with pytest.raises(ValueError, match="Batch cannot be empty"):
            AgentEventBatch(
                events=[], batch_id="test-batch-1", received_at=datetime.now()
            )

    def test_batch_validation_too_many_events(self):
        """Test batch validation with too many events."""
        # Arrange
        events = [self._create_test_event(EventType.LOGIN) for _ in range(101)]

        # Act & Assert
        with pytest.raises(ValueError, match="Batch size cannot exceed 100 events"):
            AgentEventBatch(
                events=events, batch_id="test-batch-1", received_at=datetime.now()
            )

    def _create_test_event(self, event_type: EventType) -> AgentEvent:
        """Helper method to create test agent event."""
        return AgentEvent(
            tenant_id="test-tenant",
            agent_id="test-agent",
            event_type=event_type,
            event_hash="a" * 64,
            timestamp_utc=datetime(2024, 1, 1, 12, 0, 0),
            timestamp_local=datetime(2024, 1, 1, 6, 0, 0),
            tenant_name="Test Tenant",
            timezone_name="America/Chicago",
            agent_name="Test Agent",
        )


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
