"""Command for getting or creating a ring group."""

from dataclasses import dataclass


@dataclass(frozen=True)
class GetOrCreateRingGroupCommand:
    """Command to get existing ring group or create new one.

    Attributes:
        tenant_key: Tenant key (foreign key)
        ring_group_name: Ring group name
        ring_group_uri: Ring group URI (optional)
    """

    tenant_key: int
    ring_group_name: str
    ring_group_uri: str | None = None
