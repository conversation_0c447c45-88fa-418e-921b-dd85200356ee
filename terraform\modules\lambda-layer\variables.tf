variable "layer_name" {
  type        = string
  description = "Name of the Lambda layer"
}

variable "description" {
  type        = string
  description = "Description of the Lambda layer"
}

variable "s3_bucket" {
  type        = string
  description = "S3 bucket containing the layer code"
}

variable "s3_key" {
  type        = string
  description = "S3 key for the layer zip file"
}

variable "source_code_hash" {
  type        = string
  description = "Hash of the layer source code for change detection"
}

variable "compatible_runtimes" {
  type        = list(string)
  description = "Compatible Lambda runtimes"
  default     = ["python3.12"]
}

variable "compatible_architectures" {
  type        = list(string)
  description = "Compatible architectures"
  default     = ["x86_64"]
}

variable "skip_destroy" {
  type        = bool
  description = "Whether to skip destroying the layer on terraform destroy"
  default     = false
}
