"""Database-related domain exceptions."""

from typing import Any

from smartanalytics_domain.exceptions.base import DomainError


class DatabaseConnectionError(DomainError):
    """Exception raised when a database connection fails."""

    def __init__(self, message: str, context: dict[str, Any] | None = None):
        """Initialize database connection error.

        Args:
            message: Error message
            context: Additional context information
        """
        super().__init__(
            message, error_code="DATABASE_CONNECTION_ERROR", context=context
        )


class DuplicateRecordError(DomainError):
    """Exception raised when a duplicate record is detected."""

    def __init__(self, message: str, context: dict[str, Any] | None = None):
        """Initialize duplicate record error.

        Args:
            message: Error message
            context: Additional context information
        """
        super().__init__(message, error_code="DUPLICATE_RECORD_ERROR", context=context)


class IntegrityConstraintError(DomainError):
    """Exception raised when a database integrity constraint is violated."""

    def __init__(self, message: str, context: dict[str, Any] | None = None):
        """Initialize integrity constraint error.

        Args:
            message: Error message
            context: Additional context information
        """
        super().__init__(
            message, error_code="INTEGRITY_CONSTRAINT_ERROR", context=context
        )
