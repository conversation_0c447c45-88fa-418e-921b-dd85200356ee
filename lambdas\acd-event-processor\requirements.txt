# Production dependencies for ACD Event Processor Lambda
#
# NOTE: Most dependencies are provided by Lambda layers:
#   - utilities layer: pydantic, aws-lambda-powertools, xmltodict, pytz, python-dateutil
#   - domain layer: business logic (no external deps)
#   - infrastructure layer: asyncpg, psycopg2-binary, sqlalchemy, tenacity
#
# This file contains ONLY Lambda-specific dependencies not provided by layers

# AWS Lambda typing for proper type annotations
aws-lambda-typing==2.17.0
