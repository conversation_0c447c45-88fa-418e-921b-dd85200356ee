"""Tests for database models and schema definitions."""

from smartanalytics_infrastructure.database.models import metadata


class TestDatabaseModels:
    """Test database model definitions and relationships."""

    def test_metadata_schema_configuration(self):
        """Test that metadata has correct schema configuration."""
        # Schema should be set from environment variable or default
        # Accept any valid schema name since it depends on environment configuration
        assert metadata.schema is not None
        assert isinstance(metadata.schema, str)
        assert len(metadata.schema) > 0
