"""Database session management for async operations."""

from collections.abc import Async<PERSON>enerator
from contextlib import asynccontextmanager

from smartanalytics_infrastructure.database.connection import get_database_connection
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker


class AsyncSessionFactory:
    """Factory for creating async database sessions."""

    def __init__(self) -> None:
        """Initialize session factory."""
        self._session_maker: async_sessionmaker[AsyncSession] | None = None

    def get_session_maker(self) -> async_sessionmaker[AsyncSession]:
        """Get or create async session maker.

        Returns:
            Configured async session maker
        """
        if self._session_maker is None:
            db_connection = get_database_connection()
            engine = db_connection.get_engine()

            self._session_maker = async_sessionmaker(
                engine,
                class_=AsyncSession,
                expire_on_commit=False,
                autoflush=True,
                autocommit=False,
            )

        result: async_sessionmaker[AsyncSession] = self._session_maker
        return result

    @asynccontextmanager
    async def create_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Create async database session with automatic cleanup.

        Yields:
            Async database session
        """
        session_maker = self.get_session_maker()

        async with session_maker() as session:
            try:
                yield session
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()


# Global session factory instance
_session_factory = AsyncSessionFactory()


@asynccontextmanager
async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """Get async database session.

    This is a convenience function that uses the global session factory.

    Yields:
        Async database session
    """
    async with _session_factory.create_session() as session:
        yield session
