[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "smartanalytics-domain"
version = "1.0.0"
description = "Domain core layer for SmartAnalytics applications"
authors = [
    {name = "SmartAnalytics Team"}
]
readme = "README.md"
requires-python = ">=3.12"
classifiers = [
    "Intended Audience :: Developers",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    # Layer dependencies
    "smartanalytics-utilities",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",

    # Code quality
    "black>=23.12.0",
    "ruff>=0.1.9",
    "mypy>=1.8.0",
    "bandit>=1.7.5",

    # Type stubs
    "types-python-dateutil>=2.8.19",
    "types-pytz>=2023.3.1.1",
]
