"""Event-related domain exceptions."""

from typing import Any

from smartanalytics_domain.exceptions.base import DomainError


class EventProcessingError(DomainError):
    """Base exception for event processing errors."""

    def __init__(
        self,
        message: str,
        event_data: dict[str, Any] | None = None,
        error_code: str | None = None,
        context: dict[str, Any] | None = None,
    ):
        """Initialize event processing error.

        Args:
            message: Error message
            event_data: Event data that caused the error
            error_code: Optional error code
            context: Additional context information
        """
        context = context or {}
        if event_data:
            context["event_data"] = event_data

        super().__init__(message, error_code, context)
        self.event_data = event_data


class InvalidEventDataError(EventProcessingError):
    """Exception raised when event data is invalid or malformed."""

    def __init__(
        self,
        message: str,
        event_data: dict[str, Any] | None = None,
        field_name: str | None = None,
        field_value: Any | None = None,
    ):
        """Initialize invalid event data error.

        Args:
            message: Error message
            event_data: Invalid event data
            field_name: Name of invalid field
            field_value: Value of invalid field
        """
        context = {}
        if field_name:
            context["field_name"] = field_name
        if field_value is not None:
            context["field_value"] = field_value

        super().__init__(
            message,
            event_data=event_data,
            error_code="INVALID_EVENT_DATA",
            context=context,
        )
        self.field_name = field_name
        self.field_value = field_value
