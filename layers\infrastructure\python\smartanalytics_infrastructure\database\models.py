"""SQLAlchemy models for Aurora PostgreSQL.

These models define the dimensional data warehouse schema:
- dim_tenant: Tenant dimension table
- dim_agent: Agent dimension table
- dim_ring_group: Ring group dimension table
- dim_agent_event: Agent event fact table

All constraints, indexes, and relationships are preserved from the original schema.
"""

from datetime import datetime
from typing import Optional

from smartanalytics_utilities.config.settings import get_settings
from sqlalchemy import (
    JSON,
    BigInteger,
    DateTime,
    ForeignKey,
    Index,
    Integer,
    MetaData,
    String,
    UniqueConstraint,
)
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship
from sqlalchemy.sql import func

settings = get_settings()

# Get schema from environment variable
# For testing with SQLite, use None (no schema)
_schema = settings.database.schema_name
metadata = MetaData(
    schema=_schema,
    naming_convention={
        "ix": "ix_%(column_0_label)s",
        "uq": "uq_%(table_name)s_%(column_0_name)s",
        "ck": "ck_%(table_name)s_%(constraint_name)s",
        "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
        "pk": "pk_%(table_name)s",
    },
)


# Create base class using modern SQLAlchemy 2.0 approach
class Base(DeclarativeBase):
    """Base class for all database models."""

    metadata = metadata


class DimTenant(Base):
    """Tenant dimension table."""

    __tablename__ = "dim_tenant"

    # Primary key (auto-increment)
    tenant_key: Mapped[int] = mapped_column(
        BigInteger, primary_key=True, autoincrement=True
    )

    # Business keys
    tenant_name: Mapped[str] = mapped_column(String(256), nullable=False)
    timezone_name: Mapped[str] = mapped_column(String(256), nullable=False)

    # Audit field
    created_at_utc: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, server_default=func.now()
    )

    # Relationships
    agents: Mapped[list["DimAgent"]] = relationship("DimAgent", back_populates="tenant")
    ring_groups: Mapped[list["DimRingGroup"]] = relationship(
        "DimRingGroup", back_populates="tenant"
    )
    agent_events: Mapped[list["DimAgentEvent"]] = relationship(
        "DimAgentEvent", back_populates="tenant"
    )

    # Unique constraint
    __table_args__ = (
        UniqueConstraint(
            "tenant_name", "timezone_name", name="uq_dim_tenant_tenant_name_timezone"
        ),
        Index("idx_dim_tenant_tenant_name", "tenant_name"),
    )

    def __repr__(self) -> str:
        return (
            f"DimTenant(tenant_key={self.tenant_key}, tenant_name={self.tenant_name})"
        )


class DimAgent(Base):
    """Agent dimension table."""

    __tablename__ = "dim_agent"

    # Primary key (auto-increment)
    agent_key: Mapped[int] = mapped_column(
        BigInteger, primary_key=True, autoincrement=True
    )

    # Foreign key to tenant
    tenant_key: Mapped[int] = mapped_column(
        BigInteger, ForeignKey("dim_tenant.tenant_key"), nullable=False
    )

    # Business keys
    agent_name: Mapped[str] = mapped_column(String(256), nullable=False)
    agent_role: Mapped[str | None] = mapped_column(String(256), nullable=True)

    # Audit field
    created_at_utc: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, server_default=func.now()
    )

    # Relationships
    tenant: Mapped["DimTenant"] = relationship("DimTenant", back_populates="agents")
    agent_events: Mapped[list["DimAgentEvent"]] = relationship(
        "DimAgentEvent", back_populates="agent"
    )

    # Unique constraint and indexes
    __table_args__ = (
        UniqueConstraint(
            "tenant_key",
            "agent_name",
            "agent_role",
            name="uq_dim_agent_tenant_name_agent_name_agent_role",
        ),
        Index("idx_dim_agent_agent_name", "agent_name"),
        Index("idx_dim_agent_tenant_name_agent_name", "tenant_key", "agent_name"),
        Index("idx_dim_agent_tenant_name_agent_role", "tenant_key", "agent_role"),
    )

    def __repr__(self) -> str:
        return f"DimAgent(agent_key={self.agent_key}, agent_name={self.agent_name})"


class DimRingGroup(Base):
    """Ring group dimension table."""

    __tablename__ = "dim_ring_group"

    # Primary key (auto-increment)
    ring_group_key: Mapped[int] = mapped_column(
        BigInteger, primary_key=True, autoincrement=True
    )

    # Foreign key to tenant
    tenant_key: Mapped[int] = mapped_column(
        BigInteger, ForeignKey("dim_tenant.tenant_key"), nullable=False
    )

    # Business keys
    ring_group_name: Mapped[str] = mapped_column(String(256), nullable=False)
    ring_group_uri: Mapped[str] = mapped_column(String(256), nullable=False)

    # Audit field
    created_at_utc: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True), nullable=True, server_default=func.now()
    )

    # Relationships
    tenant: Mapped["DimTenant"] = relationship(
        "DimTenant", back_populates="ring_groups"
    )
    agent_events: Mapped[list["DimAgentEvent"]] = relationship(
        "DimAgentEvent", back_populates="ring_group"
    )

    # Unique constraint and indexes
    __table_args__ = (
        UniqueConstraint(
            "tenant_key",
            "ring_group_name",
            "ring_group_uri",
            name="uq_dim_ring_group_ring_group_name_tenant_name_uri",
        ),
        Index(
            "idx_dim_ring_group_tenant_name_ring_group_name",
            "tenant_key",
            "ring_group_name",
        ),
        Index(
            "idx_dim_ring_group_tenant_name_ring_group_uri",
            "tenant_key",
            "ring_group_uri",
        ),
    )

    def __repr__(self) -> str:
        return f"DimRingGroup(ring_group_key={self.ring_group_key}, ring_group_name={self.ring_group_name})"


class DimAgentEvent(Base):
    """Agent event fact table with foreign key relationships."""

    __tablename__ = "dim_agent_event"

    # Primary key (auto-increment)
    event_key: Mapped[int] = mapped_column(
        BigInteger, primary_key=True, autoincrement=True
    )

    # Foreign keys
    agent_key: Mapped[int] = mapped_column(
        BigInteger, ForeignKey("dim_agent.agent_key"), nullable=False
    )
    ring_group_key: Mapped[int | None] = mapped_column(
        BigInteger, ForeignKey("dim_ring_group.ring_group_key"), nullable=True
    )
    tenant_key: Mapped[int] = mapped_column(
        BigInteger, ForeignKey("dim_tenant.tenant_key"), nullable=False
    )

    # Event information
    event_type: Mapped[str] = mapped_column(String(256), nullable=False)
    hash_event: Mapped[str] = mapped_column(String(64), nullable=False)

    # Timestamps
    timestamp_utc: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False
    )
    timestamp_local: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False
    )

    # Event details
    operator_id: Mapped[str | None] = mapped_column(String(256), nullable=True)
    workstation: Mapped[str | None] = mapped_column(String(256), nullable=True)
    media_label: Mapped[str | None] = mapped_column(String(256), nullable=True)
    uri: Mapped[str | None] = mapped_column(String(256), nullable=True)
    device_name: Mapped[str | None] = mapped_column(String(256), nullable=True)
    busied_out_action: Mapped[str | None] = mapped_column(String(256), nullable=True)
    busied_out_duration: Mapped[int | None] = mapped_column(Integer, nullable=True)
    reason_code: Mapped[str | None] = mapped_column(String(256), nullable=True)

    # Complete event data (JSON type works for both PostgreSQL and SQLite)
    # Renamed from json_data to event_data
    event_data: Mapped[dict] = mapped_column(JSON, nullable=False)

    # Audit field
    created_at_utc: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, server_default=func.now()
    )

    # Relationships
    agent: Mapped["DimAgent"] = relationship("DimAgent", back_populates="agent_events")
    ring_group: Mapped[Optional["DimRingGroup"]] = relationship(
        "DimRingGroup", back_populates="agent_events"
    )
    tenant: Mapped["DimTenant"] = relationship(
        "DimTenant", back_populates="agent_events"
    )

    # Unique constraint and indexes for performance
    __table_args__ = (
        UniqueConstraint("hash_event", name="uq_dim_agent_event_hash_event"),
        Index("idx_dim_agent_event_tenant_time", "tenant_key", "timestamp_utc"),
        Index(
            "idx_dim_agent_event_tenant_agent_time",
            "tenant_key",
            "agent_key",
            "timestamp_utc",
        ),
        Index(
            "idx_dim_agent_event_tenant_event_type_time",
            "tenant_key",
            "event_type",
            "timestamp_utc",
        ),
    )

    def __repr__(self) -> str:
        return f"DimAgentEvent(event_key={self.event_key}, event_type={self.event_type}, timestamp_utc={self.timestamp_utc})"
