<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="1" skipped="0" tests="41" time="2.172" timestamp="2025-10-03T17:51:05.417282" hostname="HYB-d7HUHw58Q86"><testcase classname="tests.test_agent_event.TestAgentEvent" name="test_create_valid_agent_event" time="0.004" /><testcase classname="tests.test_agent_event.TestAgentEvent" name="test_agent_event_validation_empty_tenant_name" time="0.006" /><testcase classname="tests.test_agent_event.TestAgentEvent" name="test_agent_event_validation_invalid_hash_length" time="0.003" /><testcase classname="tests.test_agent_event.TestAgentEvent" name="test_agent_event_is_login_event" time="0.001" /><testcase classname="tests.test_agent_event.TestAgentEvent" name="test_agent_event_is_logout_event" time="0.001" /><testcase classname="tests.test_agent_event.TestAgentEvent" name="test_agent_event_has_ring_group" time="0.001" /><testcase classname="tests.test_agent_event.TestAgentEvent" name="test_agent_event_to_dict" time="0.001" /><testcase classname="tests.test_agent_event.TestAgentEventBatch" name="test_create_valid_batch" time="0.002" /><testcase classname="tests.test_agent_event.TestAgentEventBatch" name="test_batch_validation_empty_events" time="0.002" /><testcase classname="tests.test_agent_event.TestAgentEventBatch" name="test_batch_validation_too_many_events" time="0.005" /><testcase classname="tests.test_commands.TestCommands" name="test_get_or_create_tenant_command" time="0.001" /><testcase classname="tests.test_commands.TestCommands" name="test_get_or_create_tenant_command_with_display_name" time="0.001" /><testcase classname="tests.test_commands.TestCommands" name="test_get_or_create_agent_command" time="0.001" /><testcase classname="tests.test_commands.TestCommands" name="test_get_or_create_ring_group_command" time="0.003" /><testcase classname="tests.test_commands.TestCommands" name="test_process_agent_event_command" time="0.002" /><testcase classname="tests.test_commands.TestCommands" name="test_process_agent_event_command_with_timezone" time="0.001" /><testcase classname="tests.test_exceptions.TestExceptions" name="test_domain_error" time="0.001" /><testcase classname="tests.test_exceptions.TestExceptions" name="test_domain_error_with_context" time="0.001" /><testcase classname="tests.test_exceptions.TestExceptions" name="test_domain_error_to_dict" time="0.001" /><testcase classname="tests.test_exceptions.TestExceptions" name="test_event_processing_error" time="0.001" /><testcase classname="tests.test_exceptions.TestExceptions" name="test_event_processing_error_with_event_data" time="0.003" /><testcase classname="tests.handlers.test_dimension_handlers" name="test_get_or_create_tenant_new" time="0.022" /><testcase classname="tests.handlers.test_dimension_handlers" name="test_get_or_create_tenant_existing" time="0.007" /><testcase classname="tests.handlers.test_dimension_handlers" name="test_get_or_create_tenant_no_display_name" time="0.017" /><testcase classname="tests.handlers.test_dimension_handlers" name="test_get_or_create_agent_new" time="0.008" /><testcase classname="tests.handlers.test_dimension_handlers" name="test_get_or_create_agent_existing" time="0.006" /><testcase classname="tests.handlers.test_dimension_handlers" name="test_get_or_create_agent_no_role" time="0.007" /><testcase classname="tests.handlers.test_dimension_handlers" name="test_get_or_create_ring_group_new" time="0.007" /><testcase classname="tests.handlers.test_dimension_handlers" name="test_get_or_create_ring_group_existing" time="0.006" /><testcase classname="tests.handlers.test_dimension_handlers" name="test_get_or_create_ring_group_no_uri" time="0.006" /><testcase classname="tests.handlers.test_dimension_handlers" name="test_command_composition_all_new" time="0.009" /><testcase classname="tests.handlers.test_dimension_handlers" name="test_command_composition_all_existing" time="0.008" /><testcase classname="tests.handlers.test_event_handlers" name="test_process_agent_event_success" time="0.179" /><testcase classname="tests.handlers.test_event_handlers" name="test_process_agent_event_duplicate" time="0.052"><failure message="AssertionError: assert False is True&#10; +  where False = SingleEventResult(success=False, event_hash='', event_type='', is_duplicate=False, error_message='uq_agent_event_hash (context: event_hash=abc123)', processing_time_ms=4.369258880615234).success">mock_uow = &lt;MagicMock id='1639121134528'&gt;
sample_login_xml = '&lt;?xml version="1.0" encoding="UTF-8"?&gt;\n&lt;LogEvent&gt;\n    &lt;timestamp&gt;2024-01-15T10:30:00Z&lt;/timestamp&gt;\n    &lt;agencyOrEle...gentRole&gt;\n        &lt;workstation&gt;WS001&lt;/workstation&gt;\n        &lt;operatorId&gt;OP123&lt;/operatorId&gt;\n    &lt;/login&gt;\n&lt;/LogEvent&gt;'

    @pytest.mark.asyncio
    async def test_process_agent_event_duplicate(mock_uow, sample_login_xml):
        """Test handling of duplicate event."""
        # Arrange
        handler = ProcessAgentEventCommandHandler(mock_uow)
        command = ProcessAgentEventCommand(
            xml_content=sample_login_xml,
            sqs_message_id="test-message-123",
            client_timezone="America/New_York",
        )
    
        # Simulate duplicate event (IntegrityError on save)
        mock_uow.agent_events.save.side_effect = DuplicateRecordError(
            "uq_agent_event_hash",
            context={"event_hash": "abc123"},
        )
        # Act
        result = await handler.handle(command)
    
        # Assert
&gt;       assert result.success is True
E       AssertionError: assert False is True
E        +  where False = SingleEventResult(success=False, event_hash='', event_type='', is_duplicate=False, error_message='uq_agent_event_hash (context: event_hash=abc123)', processing_time_ms=4.369258880615234).success

tests\handlers\test_event_handlers.py:128: AssertionError</failure></testcase><testcase classname="tests.handlers.test_event_handlers" name="test_process_agent_event_with_ring_group" time="0.016" /><testcase classname="tests.handlers.test_event_handlers" name="test_process_agent_event_existing_tenant" time="0.015" /><testcase classname="tests.handlers.test_event_handlers" name="test_process_agent_event_existing_agent" time="0.015" /><testcase classname="tests.handlers.test_event_handlers" name="test_process_agent_event_invalid_xml" time="0.010" /><testcase classname="tests.handlers.test_event_handlers" name="test_process_agent_event_missing_required_fields" time="0.009" /><testcase classname="tests.handlers.test_event_handlers" name="test_process_agent_event_timezone_conversion" time="0.027" /><testcase classname="tests.handlers.test_event_handlers" name="test_process_agent_event_hash_generation" time="0.017" /></testsuite></testsuites>